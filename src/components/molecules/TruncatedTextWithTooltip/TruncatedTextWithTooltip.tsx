import { Text, TextProps } from '@mantine/core';
import React, { ReactNode, useEffect, useRef, useState } from 'react';

import { Tooltip } from '@/components';

import styles from './truncated-text-with-tooltip.module.css';

interface TextWithTooltipProps extends TextProps {
  text: string | ReactNode;
  tooltipContentClassName?: string;
  [k: string]: unknown;
}

export const TruncatedTextWithTooltip = (props: TextWithTooltipProps) => {
  const { text, tooltipContentClassName = '', ...rest } = props;

  const [isTruncated, setIsTruncated] = useState(false);
  const textRef = useRef<HTMLParagraphElement>(null);

  useEffect(() => {
    const element = textRef.current;
    if (element) {
      setIsTruncated(element.scrollWidth > element.clientWidth);
    }
  }, [text]);

  return (
    <Tooltip
      label={text}
      disabled={!isTruncated}
      classes={{
        tooltip: styles.tooltip,
      }}
      contentClassName={`${styles.contentTooltip} ${tooltipContentClassName}`}
      tooltipWidthContent={250}
    >
      <Text {...rest} ref={textRef} truncate="end">
        {text}
      </Text>
    </Tooltip>
  );
};
