import React, { MouseEvent } from 'react';

import DotsIcon from '@/assets/images/dots.svg?react';

import { Menu, MenuOptionProps } from '../Menu';
import styles from './item-menu.module.css';

export interface ItemMenuProps {
  options: MenuOptionProps[];
}

export const ItemMenu = (props: ItemMenuProps) => {
  const { options } = props;

  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
  };

  return (
    <div className={styles.menuButton}>
      <Menu options={options} trigger="click" keepMounted>
        <button onClick={handleClick}>
          <DotsIcon />
        </button>
      </Menu>
    </div>
  );
};
