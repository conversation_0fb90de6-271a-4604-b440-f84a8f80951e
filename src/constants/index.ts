import { DefaultItemOption, SortOrderOptionType } from '@/types/utility';
import { ClinicAccountType, ClinicStatusType } from '@/types/common';
import i18n from '@/apps/shop/i18n';

export const DEFAULT_DISPLAY_DATE_FORMAT = 'MM/DD/YYYY';
export const DEFAULT_SERVER_DATE_FORMAT = 'YYYY-MM-DD';

export const SORT_ORDERS: { [k in SortOrderOptionType]: SortOrderOptionType } =
  {
    asc: 'asc',
    desc: 'desc',
  };

export const ITEMS_PER_PAGE_OPTIONS: DefaultItemOption[] = [
  {
    value: '10',
    label: '10',
  },
  {
    value: '15',
    label: '15',
  },
  {
    value: '20',
    label: '20',
  },
  {
    value: '50',
    label: '50',
  },
];

export const MODAL_NAME = {
  VENDOR_CONNECT: 'VENDOR_CONNECT',
  REMOVE_PRODUCT_FROM_CART: 'REMOVE_PRODUCT_FROM_CART',
  CLEAR_CART: 'CLEAR_CART',
  CREATE_COHORT: 'CREATE_COHORT',
  CHECKOUT_SUCCESS: 'CHECKOUT_SUCCESS',
  CHANGE_PASSWORD: 'CHANGE_PASSWORD',
} as const;

export const ADDRESS_TYPE = {
  BUSINESS: 'BUSINESS',
  SHIPPING: 'SHIPPING',
} as const;

export const VENDOR_INTEREST = {
  OPT_IN: 'OPT_IN',
  OPT_OUT: 'OPT_OUT',
  HAS_ACCOUNT: 'HAS_ACCOUNT',
} as const;

export const CLINIC_STATUS: { [k in ClinicStatusType]: ClinicStatusType } = {
  ACTIVE: 'ACTIVE',
  PROCESSING: 'PROCESSING',
  INACTIVE: 'INACTIVE',
};

export const CLINIC_ACCOUNT_TYPE: {
  [k in ClinicAccountType]: ClinicAccountType;
} = {
  SINGLE: 'SINGLE',
  GROUP: 'GROUP',
};

export const RADIO_OPTIONS = [
  {
    label: i18n.t('common.yes'),
    value: 'true',
  },
  {
    label: i18n.t('common.no'),
    value: 'false',
  },
];

export const API_ERROR_EXCEPTION = {
  NOT_FOUND: 'NotFoundException',
} as const;

export const FEATURE_FLAGS = {
  SEARCH_RESULTS_FILTER: true,
  SEARCH_RESULTS_ORDER: false,
  PRODUCT_REVIEWS: false,
  SUBSTITUTES: false,
  ORDER_STORY_COMPLETE: false,
  SETTINGS_SHOPPING_PREFERENCES: false,
  PURCHASING_PREFERENCES: false,
};

export const HF_CONTACT_EMAIL = '<EMAIL>';

export const US_STATES = [
  { label: 'Alabama', value: 'AL' },
  { label: 'Alaska', value: 'AK' },
  { label: 'Arizona', value: 'AZ' },
  { label: 'Arkansas', value: 'AR' },
  { label: 'California', value: 'CA' },
  { label: 'Colorado', value: 'CO' },
  { label: 'Connecticut', value: 'CT' },
  { label: 'Delaware', value: 'DE' },
  { label: 'Florida', value: 'FL' },
  { label: 'Georgia', value: 'GA' },
  { label: 'Hawaii', value: 'HI' },
  { label: 'Idaho', value: 'ID' },
  { label: 'Illinois', value: 'IL' },
  { label: 'Indiana', value: 'IN' },
  { label: 'Iowa', value: 'IA' },
  { label: 'Kansas', value: 'KS' },
  { label: 'Kentucky', value: 'KY' },
  { label: 'Louisiana', value: 'LA' },
  { label: 'Maine', value: 'ME' },
  { label: 'Maryland', value: 'MD' },
  { label: 'Massachusetts', value: 'MA' },
  { label: 'Michigan', value: 'MI' },
  { label: 'Minnesota', value: 'MN' },
  { label: 'Mississippi', value: 'MS' },
  { label: 'Missouri', value: 'MO' },
  { label: 'Montana', value: 'MT' },
  { label: 'Nebraska', value: 'NE' },
  { label: 'Nevada', value: 'NV' },
  { label: 'New Hampshire', value: 'NH' },
  { label: 'New Jersey', value: 'NJ' },
  { label: 'New Mexico', value: 'NM' },
  { label: 'New York', value: 'NY' },
  { label: 'North Carolina', value: 'NC' },
  { label: 'North Dakota', value: 'ND' },
  { label: 'Ohio', value: 'OH' },
  { label: 'Oklahoma', value: 'OK' },
  { label: 'Oregon', value: 'OR' },
  { label: 'Pennsylvania', value: 'PA' },
  { label: 'Rhode Island', value: 'RI' },
  { label: 'South Carolina', value: 'SC' },
  { label: 'South Dakota', value: 'SD' },
  { label: 'Tennessee', value: 'TN' },
  { label: 'Texas', value: 'TX' },
  { label: 'Utah', value: 'UT' },
  { label: 'Vermont', value: 'VT' },
  { label: 'Virginia', value: 'VA' },
  { label: 'Washington', value: 'WA' },
  { label: 'West Virginia', value: 'WV' },
  { label: 'Wisconsin', value: 'WI' },
  { label: 'Wyoming', value: 'WY' },
];
