body,
#root {
  --container-max-width: auto;
  min-height: 100vh;
  color: var(--mantine-color-dark-8);
  background: #fafafa;
}

img {
  display: block;
}

.mainSection {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 2rem 1.5rem;
  max-width: 2000px;

  .pageContent {
    width: 100%;
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
    position: relative;
    background-color: #fff;
  }
}

.loaderRoot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  height: 100vh;
}

.circleIcon {
  --icon-bg: 'black';
  border-radius: 50%;
  width: 10px;
  height: 10px;
  background-color: var(--icon-bg);
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}
