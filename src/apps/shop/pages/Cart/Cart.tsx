import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Flex, Loader } from '@mantine/core';

import { ROUTERS_PATH } from '@/apps/shop/routes/routes';

import {
  ClearCartModal,
  EmptyPage,
  RemoveItemModal,
  BudgetDetails,
} from './components';
import { CartVendorPanel } from '@/libs/cart/components/CartVendorPanel/CartVendorPanel';
import { CartSummary } from '@/libs/cart/components/CartSummary/CartSummary';
import styles from './Cart.module.css';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import { SavingAlert } from '@/libs/gpo/components/SavingAlert/SavingAlert';
import { useEffect } from 'react';
import clsx from 'clsx';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';

export const Cart = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { openModal } = useModalStore();
  const { vendors, clearCart, isCartLoading, fetchCart } = useCartStore();

  // TODO:
  // isCountError,
  const isCountError = false;

  const offers = vendors
    .flatMap(({ items }) => items)
    .map(({ quantity, product, productOfferId }) => ({
      quantity,
      product,
      offer: product.offers.find(({ id }) => id === productOfferId)!,
    }));

  const gpoSavingsTotal = offers.reduce((acc, { quantity, offer }) => {
    const { gpoSavings } = getProductOfferComputedData(offer);
    return acc + gpoSavings * quantity;
  }, 0);

  const handleGoToCheckout = () => {
    navigate(ROUTERS_PATH.checkout);
  };

  const handleOpenModal = () => {
    openModal({ name: MODAL_NAME.CLEAR_CART, clearCart });
  };

  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  return (
    <div className="mainSection">
      <PageHeader title={t('client.cart.headerTitle')} />

      <div id="cart-layout" className={styles.pageContentRoot}>
        <BudgetDetails />

        {isCartLoading && !vendors.length ? (
          <div className="loaderRoot">
            <Loader size="3rem" />
          </div>
        ) : vendors.length ? (
          <div
            className={clsx(styles.content, {
              [styles.loading]: isCartLoading,
            })}
          >
            <Flex direction="column" gap="md">
              {vendors.map((vendor) => (
                <CartVendorPanel key={vendor.id} vendor={vendor} />
              ))}
            </Flex>
            <div className={styles.checkoutBoxContainer}>
              <CartSummary
                action={{
                  onAction: handleGoToCheckout,
                  isActionBlocked: isCountError,
                  actionButtonText: t('common.checkout'),
                }}
                isLoading={isCartLoading}
                headerAction={{
                  label: 'Clear Cart',
                  handleClick: handleOpenModal,
                }}
                extraInfo={
                  gpoSavingsTotal ? (
                    <SavingAlert value={gpoSavingsTotal} />
                  ) : null
                }
              />
            </div>
          </div>
        ) : (
          <EmptyPage />
        )}
      </div>

      <RemoveItemModal />
      <ClearCartModal />
    </div>
  );
};
