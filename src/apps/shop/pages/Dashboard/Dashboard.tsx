import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import { LoadingOverlay, Text } from '@mantine/core';

import { But<PERSON>, Table } from '@/components';
import GoodIcon from '@/assets/images/dashboard/good.svg?react';
import BadIcon from '@/assets/images/dashboard/bad.svg?react';
import { get } from '@/libs/utils/api';
import { buildQueryString, getPriceString } from '@/utils';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest.ts';

import { MODAL_NAME } from '@/constants';

import {
  OVERVIEW_DATA,
  QUARTER_DATA,
  CARD_POINTS_GOOD,
  CARD_POINTS_BAD,
  MOCK_TABLE_DATA,
  QUARTER_SECOND_DATA,
} from './mock';
import {
  OverviewCard,
  BarChat,
  CardPoint,
  BlurBox,
  <PERSON><PERSON>eriod,
} from './components';
import styles from './dashboard.module.css';
import { COLUMNS } from './columns';
import { OverViewType } from './types.ts';
import { useAuthStore } from '../../stores/useAuthStore.ts';
import { useModalStore } from '../../stores/useModalStore.ts';
import { PageHeader } from '../../components/PageHeader/PageHeader.tsx';

interface OverviewCardProps {
  label: string;
  value: string;
  additionValue?: string;
}

export const Dashboard = () => {
  const { t } = useTranslation();

  const { user } = useAuthStore();
  const { openModal } = useModalStore();

  const [overviewData, setOverviewData] = useState<OverviewCardProps[]>([]);

  const { apiRequest: getData, isLoading } = useAsyncRequest({
    apiFunc: async (value?: string) => {
      const [year, month] = (value || '-').split('-');
      const query = buildQueryString({ year, month });

      const data = await get<OverViewType>({
        url: `/product/account/${user?.accountId}/dashboard${value ? `?${query}` : ''}`,
      });

      setOverviewData([
        {
          label: t('client.dashboard.revenue'),
          value: getPriceString(data.revenue),
          additionValue: t('client.dashboard.yoy'),
        },
        {
          label: t('client.dashboard.cogs'),
          value: getPriceString(data.cogs),
          additionValue: t('client.dashboard.yoy'),
        },
        {
          label: t('client.dashboard.ga'),
          value: getPriceString(data.ga),
          additionValue: t('client.dashboard.yoy'),
        },
        {
          label: t('client.dashboard.actual'),
          value: '--',
        },
        {
          label: t('client.dashboard.cogsRevenue'),
          value: `${data.cogsToRevenuePercentage}%`,
        },
        {
          label: t('client.dashboard.gaRevenue'),
          value: `${data.gaToRevenuePercentage}%`,
        },
      ]);
    },
    errorFunc: () => {
      // TODO remove
      setOverviewData([
        {
          label: t('client.dashboard.revenue'),
          value: '$14,332,231',
          additionValue: t('client.dashboard.yoy'),
        },
        {
          label: t('client.dashboard.cogs'),
          value: '$3,009,768',
          additionValue: t('client.dashboard.yoy'),
        },
        {
          label: t('client.dashboard.ga'),
          value: '$1,003,262',
          additionValue: t('client.dashboard.yoy'),
        },
        {
          label: t('client.dashboard.actual'),
          value: '85%',
        },
        {
          label: t('client.dashboard.cogsRevenue'),
          value: '21%',
        },
        {
          label: t('client.dashboard.gaRevenue'),
          value: '7%',
        },
      ]);
    },
  });

  useEffect(() => {
    getData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleOpenCreateCohort = () => {
    openModal({
      name: MODAL_NAME.CREATE_COHORT,
    });
  };

  return (
    <div className={clsx('mainSection', styles.mainSection)}>
      <PageHeader
        title={t('client.dashboard.title')}
        contentRootClass={styles.header}
      >
        <Button size="md" onClick={handleOpenCreateCohort}>
          {t('client.dashboard.createCohort')}
        </Button>

        <TimePeriod getData={getData} />
      </PageHeader>

      <div className={clsx('pageContent', styles.pageContentRoot)}>
        <Text size="lgMd" fw={700} mb="1.5rem">
          {t('client.dashboard.overview')}
        </Text>

        <div className={styles.list}>
          <LoadingOverlay visible={isLoading} />

          {overviewData.map((item) => (
            <OverviewCard
              key={item.label}
              title={item.label}
              value={item.value}
              additionValue={item.additionValue}
            />
          ))}
        </div>

        <Text size="lgMd" fw={700} mb="1.5rem">
          {t('client.dashboard.charts')}
        </Text>

        <div className={styles.list}>
          <BarChat
            data={QUARTER_DATA}
            title={t('client.dashboard.corporateRevenue')}
            subtitle={t('client.dashboard.yearOverYear')}
          />
          <BarChat
            data={QUARTER_SECOND_DATA}
            title={t('client.dashboard.corporateCogsRevenue')}
            subtitle={t('client.dashboard.yearOverYear')}
          />
          <BarChat
            data={OVERVIEW_DATA}
            title={t('client.dashboard.budgetCOGSRevenue')}
            subtitle={t('client.dashboard.yearOverYear')}
          />
        </div>

        <Text size="lgMd" fw={700} mb="1.5rem">
          {t('client.dashboard.overview')}
        </Text>

        <div className={styles.list}>
          <CardPoint
            title={t('client.dashboard.cardPointTitle')}
            points={CARD_POINTS_GOOD}
            color="var(--mantine-color-green-11)"
            icon={<GoodIcon />}
          />

          <CardPoint
            title={t('client.dashboard.cardPointTitle')}
            points={CARD_POINTS_BAD}
            color="var(--mantine-color-red-2)"
            icon={<BadIcon />}
          />
        </div>

        <Text size="lgMd" fw={700} mb="1.5rem" w="100%">
          {t('client.dashboard.vendors')}
        </Text>

        <BlurBox>
          <Table data={MOCK_TABLE_DATA} columns={COLUMNS} />
        </BlurBox>
      </div>
    </div>
  );
};
