import {
  Chart as ChartJS,
  LinearScale,
  CategoryScale,
  BarElement,
  PointElement,
  LineElement,
  Legend,
  ChartOptions,
  ChartData,
} from 'chart.js/auto';
import { Chart } from 'react-chartjs-2';
import { Text } from '@mantine/core';

import { BlurBox } from '../BlurBox';
import styles from './bar-chart.module.css';

ChartJS.register(
  LinearScale,
  CategoryScale,
  BarElement,
  PointElement,
  LineElement,
  Legend,
);

const OPTION = {
  responsive: false,
  maintainAspectRatio: false,
  scales: {
    y: {
      grid: {
        display: true,
        drawTicks: false,
      },
      ticks: {
        display: false,
      },
      display: true,
    },
    x: {
      grid: {
        display: false,
      },
    },
  },
  plugins: {
    datalabels: {
      anchor: 'end',
      align: 'start',
      color: '#222222',
      backgroundColor: '#F4F4F480',
      borderRadius: 4,
      borderWidth: 1,
      borderColor: '#F4F4F4F4',
      padding: 4,
      font: {
        size: 12,
        weight: 700,
      },
    },
    legend: {
      position: 'bottom',
      labels: {
        usePointStyle: true,
        pointStyle: 'circle',
        font: {
          size: 14,
        },
        padding: 20,
        generateLabels: function (chart) {
          const { datasets } = chart.data;
          return datasets.map((dataset, i) => {
            return {
              text: dataset.label,
              fillStyle: dataset.borderColor,
              strokeStyle: dataset.borderColor,
              lineWidth: 2,
              hidden: !chart.isDatasetVisible(i),
              index: i,
            };
          });
        },
      },
    },
  },
} as ChartOptions<'bar'>;

interface BarChatProps {
  data: unknown;
  title: string;
  subtitle: string;
}

export const BarChat = (props: BarChatProps) => {
  const { data, subtitle, title } = props;

  return (
    <div className={styles.wrap}>
      <Text size="lgMd" fw={700}>
        {title}
      </Text>

      <Text size="sm" c="dark.5" mb="2rem">
        {subtitle}
      </Text>

      <BlurBox>
        <Chart
          type="bar"
          width="309px"
          height="300px"
          data={data as ChartData<'bar', 'bar', unknown>}
          options={OPTION}
        />
      </BlurBox>
    </div>
  );
};
