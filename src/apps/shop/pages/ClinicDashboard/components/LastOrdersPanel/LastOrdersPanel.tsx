import { Box, Text, Title } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { OrderHistoryItem } from '@/apps/shop/pages/OrderHistory/components/OrderHistoryItem/OrderHistoryItem';
import { useOrderList } from '@/apps/shop/pages/OrderHistory/services/useOrderList';
import styles from './LastOrdersPanel.module.css';

export const LastOrdersPanel = () => {
  const { orders } = useOrderList({ limit: 4 });

  return (
    <CollapsiblePanel
      header={
        <Title order={4} px="1.5rem" py="1.3rem" fw="500">
          Last Orders
        </Title>
      }
      content={
        <Flex direction="column" p="1.5rem" bg="white">
          <Title order={4} fw="500">
            View your most recent orders.
          </Title>

          <Text size="md" mb="1.5rem">
            Click on any order to see full details.
          </Text>

          <Box p="md" bg="rgba(0, 0, 0, 0.02)">
            {orders.length ? (
              orders.map((order) => (
                <Box
                  key={order.id}
                  mb="md"
                  bg="#FFF"
                  className={styles.itemContainer}
                >
                  <OrderHistoryItem order={order} isActive={false} />
                </Box>
              ))
            ) : (
              <Text>No order created yet!</Text>
            )}
          </Box>
        </Flex>
      }
      startOpen
    />
  );
};
