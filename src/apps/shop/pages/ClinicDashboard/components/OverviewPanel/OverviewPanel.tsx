import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Box, Flex, Text, Title, UnstyledButton } from '@mantine/core';
import { get } from '@/libs/utils/api';
import { useCallback, useEffect, useState } from 'react';
import CalendarSearchIcon from './assets/calendar-search.svg?react';
import TrashIcon from './assets/trash.svg?react';
import {
  DateFilter,
  DateFilterProps,
} from '@/libs/date-filter/components/DateFilter/DateFilter';
import { OverviewPanelCard } from './components/OverviewPanelCard';
import { OverviewPanelCardLoader } from './components/OverviewPanelCardLoader';
import dayjs from 'dayjs';
import { DEFAULT_SERVER_DATE_FORMAT } from '@/constants';
import { formatDateRange } from './util/formatDateRange';

type OverviewType = {
  title: string;
  value: string;
  description: string;
  helpText: string | null;
}[];

export const OverviewPanel = () => {
  const firstDayOfYear = dayjs()
    .startOf('year')
    .format(DEFAULT_SERVER_DATE_FORMAT);
  const today = dayjs().format(DEFAULT_SERVER_DATE_FORMAT);
  const [data, setData] = useState<OverviewType>();
  const [dateFilter, setDateFilter] = useState({
    from: firstDayOfYear,
    to: today,
  });
  const [isDataLoading, setIsDataLoading] = useState(false);

  const fetchOverviewData = useCallback(async () => {
    setIsDataLoading(true);
    const dateFilterQueryString = `?filter[date_from]=${dateFilter.from}&filter[date_to]=${dateFilter.to}`;
    const response = await get<OverviewType[0]>({
      url: `/dashboard/metrics/total-spend${dateFilterQueryString}`,
    });

    setIsDataLoading(false);
    setData([response]);
  }, [dateFilter.from, dateFilter.to, setIsDataLoading]);

  useEffect(() => {
    fetchOverviewData();
  }, [fetchOverviewData]);

  const handleDateFilterChange: DateFilterProps['onChange'] = ({
    from,
    to,
  }) => {
    setDateFilter({ from, to });
  };

  const handleClearFilter = () => {
    setDateFilter({ from: firstDayOfYear, to: today });
  };

  const dateRange = formatDateRange(dateFilter.from, dateFilter.to);
  const isInitialDateRange =
    dateFilter.from === firstDayOfYear && dateFilter.to === today;

  return (
    <CollapsiblePanel
      header={
        <Flex align="center" justify="space-between" w="100%" pr="4rem">
          <Title order={4} px="1.5rem" py="1.3rem" fw="500">
            Overview
          </Title>
          <DateFilter
            applyedValues={dateFilter}
            onChange={handleDateFilterChange}
            label="Filter by Date"
            icon={<CalendarSearchIcon />}
            onClearFilters={handleClearFilter}
          />
        </Flex>
      }
      content={
        <Box bg="#fff" p="1.5rem">
          <Flex bg="rgba(0, 0, 0, 0.02)" p="md" gap="1rem">
            {!data || isDataLoading ? (
              <OverviewPanelCardLoader />
            ) : (
              data.map((props) => (
                <OverviewPanelCard
                  key={props.title}
                  {...props}
                  footer={
                    dateFilter.from || dateFilter.to ? (
                      <Flex align="center" gap="10px">
                        <Text size="12px" c="#666">
                          Results filtered by:{' '}
                          <Text c="#333" fw="500" span>
                            {dateRange}
                          </Text>
                        </Text>
                        {isInitialDateRange ? null : (
                          <UnstyledButton onClick={handleClearFilter}>
                            <TrashIcon />
                          </UnstyledButton>
                        )}
                      </Flex>
                    ) : null
                  }
                />
              ))
            )}
          </Flex>
        </Box>
      }
      startOpen
    />
  );
};
