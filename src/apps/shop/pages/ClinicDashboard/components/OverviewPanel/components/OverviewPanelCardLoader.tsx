import { Box, Flex } from '@mantine/core';
import 'react-loading-skeleton/dist/skeleton.css';

import Skeleton from 'react-loading-skeleton';
import styles from './OverviewPanelCard.module.css';

export const OverviewPanelCardLoader = () => {
  return (
    <Flex className={styles.container} gap="5rem" p="2rem" align="center">
      <Box w="150px">
        <Box mb="0.75rem">
          <Skeleton height={22} />
        </Box>
        <Skeleton height={16} />
      </Box>
      <Box w="200px">
        <Skeleton height={38} />
      </Box>
    </Flex>
  );
};
