import dayjs from 'dayjs';
import { Flex, Text, Tooltip } from '@mantine/core';
import styles from './RebateRemainingTime.module.css';
import clsx from 'clsx';
import { getRemainingDays } from '../../utils/getRemainingDays';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
interface RebateRemainingTimeProps {
  startDate: string;
  endDate: string;
}
export const RebateRemainingTime = ({
  startDate,
  endDate,
}: RebateRemainingTimeProps) => {
  const startDateObj = dayjs(startDate);
  const endDateObj = dayjs(endDate);
  const remainingDays = getRemainingDays(endDate);
  const areFewRemainingDays = remainingDays < 7;

  return (
    <Tooltip
      position="right-start"
      offset={{ mainAxis: -50, crossAxis: 40 }}
      label={`Start: ${startDateObj.format(DEFAULT_DISPLAY_DATE_FORMAT)} - End: ${endDateObj.format(DEFAULT_DISPLAY_DATE_FORMAT)}`}
    >
      <Flex
        px="md"
        py="0.6rem"
        align="center"
        gap="0.25rem"
        className={clsx(styles.container, {
          [styles.warn]: !areFewRemainingDays,
          [styles.danger]: areFewRemainingDays,
        })}
      >
        <Text size="0.875rem" lh="0.625rem" fw="700">
          {remainingDays}
        </Text>
        <Text size="0.875rem" lh="0.625rem">
          days remaining
        </Text>
      </Flex>
    </Tooltip>
  );
};
