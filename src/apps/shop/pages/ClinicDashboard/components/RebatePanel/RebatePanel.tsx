import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { RebateType } from '@/types/common';
import { getPriceString } from '@/utils';
import { Box, Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { EstimateRebateProgress } from '../EstimateRebateProgress/EstimateRebateProgress';
import { getRemainingDays } from '../../utils/getRemainingDays';
import dayjs from 'dayjs';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { SuggestedOfferList } from '@/libs/products/components/SuggestedOfferList/SuggestedOffersList';

interface RebatePanelProps {
  rebate: RebateType;
}
export const RebatePanel = ({ rebate }: RebatePanelProps) => {
  const remainingDays = getRemainingDays(rebate.promotion.endedAt);
  const startDate = dayjs(rebate.promotion.startedAt).format(
    DEFAULT_DISPLAY_DATE_FORMAT,
  );
  const endDate = dayjs(rebate.promotion.endedAt).format(
    DEFAULT_DISPLAY_DATE_FORMAT,
  );
  const hasSuggestedOffers = rebate.suggestedProductOffers.length > 0;

  return (
    <CollapsiblePanel
      header={
        <Flex
          p="md"
          pr={hasSuggestedOffers ? '4rem' : 'md'}
          align="center"
          justify="space-between"
          w="100%"
        >
          <Flex align="center">
            <Box>
              <Text
                c={remainingDays < 7 ? '#A31838' : '#ED7F02'}
                size="0.75rem"
                mb="0.5rem"
              >
                <Text fw="700" span>
                  {remainingDays}
                </Text>{' '}
                {remainingDays > 1 ? 'days ' : 'day '} remaining in rebate
                period
              </Text>
              <Text size="1rem" fw="700" mb="0.5rem" w="260px">
                {rebate.promotion.name}
              </Text>
              <Flex align="center" gap="0.25rem">
                <Text size="0.75rem" c="#666">
                  Start:{' '}
                  <Text c="#333" fw="700" span>
                    {startDate}
                  </Text>
                </Text>
                {' - '}
                <Text size="0.75rem" c="#666">
                  End:{' '}
                  <Text c="#333" fw="700" span>
                    {endDate}
                  </Text>
                </Text>
              </Flex>
            </Box>
            <Divider orientation="vertical" mx="24px" />
            <Box>
              <Text c="#344054" size="0.75rem" w="100px">
                Amount spent
              </Text>
              <Text c="#344054" fw="700" size="0.875rem" mt="0.5rem">
                {getPriceString(rebate.currentSpendAmount)}
              </Text>
            </Box>
            <Divider orientation="vertical" mx="24px" />
            <Box>
              <Text c="#344054" size="0.75rem" w="100px">
                Estimated rebate
              </Text>
              <Text c="#344054" fw="700" size="0.875rem" mt="0.5rem">
                {getPriceString(rebate.estimatedRebateAmount)}
              </Text>
            </Box>
          </Flex>
          <Box ml="24px">
            <EstimateRebateProgress {...rebate} />
          </Box>
        </Flex>
      }
      content={
        hasSuggestedOffers ? (
          <Box px="md" pb="md" bg="#fff">
            <Box p="md" bg="rgba(0, 0, 0, 0.02)">
              <SuggestedOfferList offers={rebate.suggestedProductOffers} />
            </Box>
          </Box>
        ) : null
      }
      variant="clean"
    />
  );
};
