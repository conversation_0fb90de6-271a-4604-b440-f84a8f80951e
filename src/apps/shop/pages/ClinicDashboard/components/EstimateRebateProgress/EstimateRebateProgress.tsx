import { Box, Divider, Flex, Progress, Text } from '@mantine/core';
import styles from './EstimateRebateProgress.module.css';
import { getPriceString } from '@/utils';
import clsx from 'clsx';

interface EstimateRebateProgressProps {
  currentRebatePercent: number;
  nextTierRebatePercent: number;
  nextTierMinimumSpendAmountThreshold: string;
  currentSpendAmount: string;
}
export const EstimateRebateProgress = ({
  currentRebatePercent,
  nextTierRebatePercent,
  nextTierMinimumSpendAmountThreshold,
  currentSpendAmount,
}: EstimateRebateProgressProps) => {
  const progressPercentage =
    (+currentSpendAmount * 100) / +nextTierMinimumSpendAmountThreshold;

  const isCompleted =
    progressPercentage >= 100 || +nextTierMinimumSpendAmountThreshold === 0;

  const startPercentage = nextTierRebatePercent ? currentRebatePercent : 0;
  const endPercentage = nextTierRebatePercent
    ? nextTierRebatePercent
    : currentRebatePercent;

  return (
    <Flex
      className={styles.container}
      align="center"
      h="2.125rem"
      pl="1.125rem"
      pr="0.625rem"
    >
      <Text size="0.75rem">
        {isCompleted ? (
          <>Earning Full Rebate 🎉</>
        ) : (
          <>
            <Text fw="700" mr="0.25rem" span>
              {getPriceString(
                +nextTierMinimumSpendAmountThreshold - +currentSpendAmount,
              )}
            </Text>
            until next tier
          </>
        )}
      </Text>
      <Box h="1rem">
        <Divider
          orientation="vertical"
          h="100%"
          mx="md"
          color="rgba(0, 0, 0, 0.15)"
        />
      </Box>
      <Flex align="center" gap="0.5rem">
        {isCompleted ? (
          <Flex className={clsx(styles.pill, styles.active)}>
            {currentRebatePercent}%
          </Flex>
        ) : (
          <>
            <Flex className={clsx(styles.pill, styles.active)}>
              {startPercentage}%
            </Flex>
            <Progress
              radius="lg"
              value={progressPercentage}
              miw="5rem"
              color="#518EF8"
            />
            <Flex className={styles.pill}>{endPercentage}%</Flex>
          </>
        )}
      </Flex>
    </Flex>
  );
};
