import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { Box, Text, Title } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { LastOrdersPanel } from './components/LastOrdersPanel/LastOrdersPanel';
import { EstimatedRebatesPanel } from './components/EstimatedRebatesPanel/EstimatedRebatesPanel';
import { OverviewPanel } from './components/OverviewPanel/OverviewPanel';

export const ClinicDashboard = () => {
  const { user } = useAuthStore();

  return (
    <div className={'mainSection'}>
      <Flex direction="column" mb="xl">
        <Title order={3} size="h4" mb=".25rem">
          Welcome {user?.name}
        </Title>

        <Text size="md">
          View your clinics spend, rebate tracking, and order history below.
        </Text>
      </Flex>

      <Box mb="2rem">
        <OverviewPanel />
      </Box>

      <Box mb="2rem">
        <EstimatedRebatesPanel />
      </Box>

      <LastOrdersPanel />
    </div>
  );
};
