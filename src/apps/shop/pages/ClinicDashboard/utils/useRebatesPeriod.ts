import { useState, useMemo } from 'react';
import dayjs, { type Dayjs } from 'dayjs';

type PeriodOption = 'entire year' | 'Q1' | 'Q2' | 'Q3' | 'Q4';

interface RabatesPeriod {
  period: PeriodOption;
  startDate: Dayjs;
  endDate: Dayjs;
  setPeriod: (period: PeriodOption) => void;
  options: PeriodOption[];
}

export const useRebatesPeriod = (): RabatesPeriod => {
  const options: PeriodOption[] = ['entire year', 'Q1', 'Q2', 'Q3', 'Q4'];

  const [period, setPeriod] = useState<PeriodOption>('entire year');

  const currentYear = dayjs().year();

  const { startDate, endDate } = useMemo(() => {
    switch (period) {
      case 'Q1':
        return {
          startDate: dayjs(`${currentYear}-01-01`),
          endDate: dayjs(`${currentYear}-03-31`),
        };
      case 'Q2':
        return {
          startDate: dayjs(`${currentYear}-04-01`),
          endDate: dayjs(`${currentYear}-06-30`),
        };
      case 'Q3':
        return {
          startDate: dayjs(`${currentYear}-07-01`),
          endDate: dayjs(`${currentYear}-09-30`),
        };
      case 'Q4':
        return {
          startDate: dayjs(`${currentYear}-10-01`),
          endDate: dayjs(`${currentYear}-12-31`),
        };
      case 'entire year':
      default:
        return {
          startDate: dayjs(`${currentYear}-01-01`),
          endDate: dayjs(`${currentYear}-12-31`),
        };
    }
  }, [period, currentYear]);

  return { period, startDate, endDate, setPeriod, options };
};
