import { Box, Button, Divider, Flex, Text } from '@mantine/core';

import { useOrderDetails } from '../../services/useOrderDetails';
import { getPriceString } from '@/utils';
import styles from './OrderDetails.module.css';
import { FEATURE_FLAGS } from '@/constants';
import { OrderVendorPanel } from '../OrderVendorPanel/OrderVendorPanel';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';
import { DownloadInvoicesLink } from '@/libs/orders/components/DownloadInvoicesLink/DownloadInvoicesLink';
import { DownloadChecklist } from '@/libs/orders/components/DownloadChecklist/DownloadChecklist';

interface OrderDetailsProps {
  id: string;
}
export const OrderDetails = ({ id }: OrderDetailsProps) => {
  const { order, isLoading } = useOrderDetails({ id });
  const {
    orderNumber,
    totalPrice,
    downloadInvoicesUrl,
    downloadChecklistUrl,
    vendorOrders = [],
  } = order ?? {};

  if (isLoading || !order) {
    return <ContentLoader />;
  }

  return (
    <Flex p="1.5rem" direction="column" className={styles.container}>
      <Flex gap="xl">
        <Box>
          <Box pb="md">
            <Text c="#666" size="xs" mb="xs">
              Order ID
            </Text>
            <Text c="#333" size="xlLg" fw="bold" inline>
              {orderNumber}
            </Text>
          </Box>
          <Divider mb="md" />
          <Box pb="md">
            <Text c="#666" size="xs">
              Order Total
            </Text>
            <Text c="#333" size="xlLg" fw="bold">
              {getPriceString(totalPrice)}
            </Text>
          </Box>
          <Box pb="md">
            <Divider mb="md" />
            <Flex direction="column">
              {downloadInvoicesUrl && (
                <DownloadInvoicesLink url={downloadInvoicesUrl} />
              )}
              {downloadChecklistUrl && (
                <DownloadChecklist url={downloadChecklistUrl} />
              )}
            </Flex>
          </Box>
          {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
            <>
              <Flex direction="column">
                <Button>Repeat Order</Button>
              </Flex>
            </>
          )}
        </Box>
        <Box style={{ flexGrow: 1 }}>
          <Text c="#666" size="xs" mb="xs">
            Order Details
          </Text>
          <Box p="lg" bg="#F8FBFD" className={styles.info}>
            {vendorOrders.map(
              // TODO: Sync with BE missing fields
              (
                { items, vendor, totalTaxFee, shippingFee, totalPrice },
                index,
              ) => (
                <Box key={vendor.id}>
                  {index !== 0 ? <Divider my="md" /> : null}
                  <Flex>
                    <Box flex="1">
                      <Text c="#666" fw="400">
                        Vendor
                      </Text>
                      <Text c="#333" fw="500">
                        {vendor.name}
                      </Text>
                    </Box>
                    <Divider mx="md" orientation="vertical" />
                    <Box flex="1">
                      <Text c="#666" fw="400">
                        Line items
                      </Text>
                      <Text c="#333" fw="500">
                        {items.length}
                      </Text>
                    </Box>
                    <Divider mx="md" orientation="vertical" />
                    <Box flex="1">
                      <Text c="#666" fw="400">
                        Taxes
                      </Text>
                      <Text c="#333" fw="500">
                        {totalTaxFee ? getPriceString(totalTaxFee) : '–'}
                      </Text>
                    </Box>
                    <Divider mx="md" orientation="vertical" />
                    <Box flex="1">
                      <Text c="#666" fw="400">
                        Shipping
                      </Text>
                      <Text c="#333" fw="500">
                        {shippingFee ? getPriceString(shippingFee) : '–'}
                      </Text>
                    </Box>
                    <Divider mx="md" orientation="vertical" />
                    <Box flex="1">
                      <Text c="#666" fw="400">
                        Vendor total
                      </Text>
                      <Text c="#333" fw="500">
                        {getPriceString(totalPrice)}
                      </Text>
                    </Box>
                  </Flex>
                </Box>
              ),
            )}
          </Box>
        </Box>
      </Flex>
      <Divider my="xl" />
      {vendorOrders.map(({ vendor, items, totalPrice }) => (
        <Box key={vendor.id} mb="md">
          <OrderVendorPanel
            totalPrice={+totalPrice}
            totalItems={items.length}
            items={items}
            vendor={vendor}
          />
        </Box>
      ))}
    </Flex>
  );
};
