import clsx from 'clsx';
import { Box, Flex } from '@mantine/core';
import { OrderDetails } from '../OrderDetails/OrderDetails';
import { OrderHistoryItemType } from '@/libs/orders/types';
import styles from './OrderHistoryContent.module.css';
import { OrderHistoryItem } from '../OrderHistoryItem/OrderHistoryItem';

interface OrderHistoryContentProps {
  orders: OrderHistoryItemType[];
  selectedOrderId?: string;
}

export const OrderHistoryContent = ({
  orders,
  selectedOrderId,
}: OrderHistoryContentProps) => {
  return (
    <Flex className={clsx('pageContent', styles.content)}>
      <Flex direction="column" w="25%">
        {orders.map((order) => (
          <OrderHistoryItem
            key={order.id}
            order={order}
            isActive={order.id === selectedOrderId}
          />
        ))}
      </Flex>
      <Box w="75%" pos="relative">
        {selectedOrderId && (
          <OrderDetails key={selectedOrderId} id={selectedOrderId} />
        )}
      </Box>
    </Flex>
  );
};
