import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Box, Button, Divider, Flex, Image, Text } from '@mantine/core';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import CartIcon from '@/assets/images/cart/cart-summary.svg?react';
import PlusIcon from '@/assets/images/plus.svg?react';
import { OrderStatus } from '../OrderStatus/OrderStatus';
import { getPriceString } from '@/utils';
import { FEATURE_FLAGS } from '@/constants';
import type {
  OrderHistoryDetailItemType,
  OrderHistoryDetailVendorOrderType,
} from '@/libs/orders/types';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { Link } from 'react-router-dom';
import styles from './OrderVendorPanel.module.css';

interface OrderVendorPanelProps {
  vendor: OrderHistoryDetailVendorOrderType['vendor'];
  items: OrderHistoryDetailItemType[];
  totalPrice: number;
  totalItems: number;
}

export const OrderVendorPanel = ({
  vendor,
  totalPrice,
  totalItems,
  items,
}: OrderVendorPanelProps) => (
  <CollapsiblePanel
    startOpen
    header={
      <Flex align="center" pr="5rem">
        <Image
          src={vendor.imageUrl}
          alt={vendor.name}
          fallbackSrc={defaultProductImgUrl}
          height={42}
        />
        <Box ml="md">
          <Text c="#333" size="md" fw="500">
            {getPriceString(totalPrice)}
            <Text c="#666" size="xs" span ml="0.2rem">
              ({totalItems} Items)
            </Text>
          </Text>
        </Box>
      </Flex>
    }
    content={
      <Flex p="md" direction="column">
        {items.map(
          (
            {
              id,
              unitPrice,
              totalPrice,
              quantity,
              orderNumber,
              status,
              product,
              productOfferId,
            },
            index,
          ) => {
            return (
              <Box key={id}>
                {index !== 0 ? <Divider my="md" /> : null}
                <ProductCartHorizontal
                  product={product}
                  productOfferId={productOfferId}
                  content={
                    <Flex>
                      <Box ml="md">
                        <Text
                          fw="500"
                          size="xs"
                          mb="0.1rem"
                          c="rgba(102, 102, 102, 0.70)"
                        >
                          Order ID: {orderNumber}
                        </Text>
                        <Link
                          to={getProductUrl(product.id, productOfferId)} // TODO: Waiting this value from API, the API needs to update
                          className={styles.titleWrap}
                        >
                          <Text fw="500" c="#333">
                            {product.name}
                          </Text>
                        </Link>
                        <Flex mt="lg">
                          <Text py="sm" pr="sm" miw="6rem" size="xs" c="#666">
                            Quantity:{' '}
                            <Text c="#333" fw="700" span>
                              {quantity}
                            </Text>
                          </Text>
                          <Divider orientation="vertical" />
                          <Text p="sm" size="xs" c="#666">
                            Price:{' '}
                            <Text c="#333" fw="700" span>
                              {getPriceString(unitPrice)}
                            </Text>
                          </Text>
                          <Divider orientation="vertical" />
                          <Text p="sm" size="xs" c="#666">
                            Net Total:{' '}
                            <Text c="#333" fw="700" span>
                              {getPriceString(totalPrice)}
                            </Text>
                          </Text>
                        </Flex>
                      </Box>
                    </Flex>
                  }
                  actions={
                    <Box ta="right">
                      <Text
                        fw="500"
                        size="xs"
                        mb="0.1rem"
                        c="rgba(102, 102, 102, 0.70)"
                      >
                        Status
                      </Text>
                      <OrderStatus status={status} align="right" />
                      {FEATURE_FLAGS.ORDER_STORY_COMPLETE && (
                        <Button mt="0.5rem">
                          <CartIcon />
                          <PlusIcon />
                        </Button>
                      )}
                    </Box>
                  }
                />
              </Box>
            );
          },
        )}
      </Flex>
    }
  />
);
