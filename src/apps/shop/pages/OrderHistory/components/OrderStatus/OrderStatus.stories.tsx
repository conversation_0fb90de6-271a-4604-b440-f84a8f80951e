import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Box, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { ORDER_STATUS_CONFIGS } from './constants';
import { ShippingStatusType } from '@/libs/orders/types';
import { OrderStatus } from './OrderStatus';

type Story = StoryObj<typeof OrderStatus>;

const Wrap = () => {
  return (
    <Flex gap="xl">
      <Box p="lg">
        <Text fw="bold" mb="lg">
          Align: Left (Default)
        </Text>
        {Object.keys(ORDER_STATUS_CONFIGS).map((status) => (
          <Box mb="md" key={status}>
            <OrderStatus
              status={status as unknown as ShippingStatusType}
              align="left"
              showStepProgress
            />
          </Box>
        ))}
      </Box>
      <Box p="lg">
        <Text fw="bold" mb="lg" ta="center">
          Align: Center
        </Text>
        {Object.keys(ORDER_STATUS_CONFIGS).map((status) => (
          <Box mb="md" key={status}>
            <OrderStatus
              status={status as unknown as ShippingStatusType}
              align="center"
              showStepProgress
            />
          </Box>
        ))}
      </Box>
      <Box p="lg">
        <Text fw="bold" mb="lg" ta="right">
          Align: Right
        </Text>
        {Object.keys(ORDER_STATUS_CONFIGS).map((status) => (
          <Box mb="md" key={status}>
            <OrderStatus
              status={status as unknown as ShippingStatusType}
              align="right"
              showStepProgress
            />
          </Box>
        ))}
      </Box>
    </Flex>
  );
};

const meta: Meta<typeof OrderStatus> = {
  title: 'Product/OrderStatus',
  component: Wrap,
};
export default meta;

export const Default: Story = {
  args: {},
};
