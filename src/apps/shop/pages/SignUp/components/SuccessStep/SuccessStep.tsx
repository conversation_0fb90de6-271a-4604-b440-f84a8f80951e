import { Box, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { OnboardingStep } from '../OnboardingStep/OnboardingStep';
import { Logo } from '@/libs/ui/Logo/Logo';
import { Link, useNavigate } from 'react-router-dom';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';
import { Button } from '@/libs/ui/Button/Button';
import MailIcon from './assets/mail.svg?react';
import styles from './SuccessStep.module.css';

interface SuccessStepProps {
  onComplete: VoidFunction;
  gpoLogoUrl: string | null;
}
export const SuccessStep = ({ gpoLogoUrl }: SuccessStepProps) => {
  const navigate = useNavigate();
  return (
    <OnboardingStep
      title={
        <>
          Congratulations! <br /> Your Account Is Ready
        </>
      }
      subTitle="Explore exclusive offers, streamline your ordering, and access tools built to support your clinic’s success."
      currentStep={3}
      totalSteps={3}
      gpoLogoUrl={gpoLogoUrl}
    >
      <Box my="48px" w="200px">
        <Logo type="emblem" />
      </Box>
      <Box mt="2rem" w="100%">
        <Button
          type="submit"
          form="stepForm"
          onClick={() => {
            navigate(ROUTERS_PATH.vendors);
          }}
        >
          Set Up Your Vendors
        </Button>
      </Box>

      <Box mt="1.5rem">
        <Link to="/">
          <Text c="#333" size="16px">
            Go to Home Page
          </Text>
        </Link>
      </Box>

      <Flex mt="2rem" p="md" bg="#F2F4F7" className={styles.infoBox} gap="md">
        <Box>
          <MailIcon />
        </Box>
        <Box>
          <Text fw="500" size="16px" c="#222" lh="1.5">
            Heads up! Please check your email.
          </Text>
          <Text c="#555F74" size="14px" lh="1.5">
            We’ve sent a welcome message with next steps and tips to get
            started. If it’s not in your inbox, check your spam folder.
          </Text>
        </Box>
      </Flex>
    </OnboardingStep>
  );
};
