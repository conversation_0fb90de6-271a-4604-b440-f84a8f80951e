import { <PERSON>, Divider, Flex, Text } from '@mantine/core';
import { OnboardingStep } from '../OnboardingStep/OnboardingStep';
import { Input } from '@/libs/form/Input';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import { einMask, phoneMask, postalCodeMask } from '@/libs/form/masks';
import { post } from '@/libs/utils/api';
import { Select } from '@/libs/form/Select';
import { US_STATES } from '@/constants';
import { OnboardingStepFormWrapper } from '../OnboardingStep/OnboardingStepFormWrapper';
import { Button } from '@/libs/ui/Button/Button';
import { useState } from 'react';
import { errorNotification } from '@/utils';

const SCHEMA = Yup.object({
  name: Yup.string().required('Name is required').max(255),
  businessTaxId: Yup.string()
    .required('Business Tax ID is required')
    .matches(/^\d{2}-\d{7}$/, 'Invalid EIN format'),
  phoneNumber: Yup.string().required('Phone number is required').max(255),
  address: Yup.object({
    street: Yup.string().required('Street is required').max(255),
    city: Yup.string().required('City is required').max(255),
    state: Yup.string().required('State is required').max(255),
    postalCode: Yup.string()
      .required('Postal code is required')
      .matches(/^\d{5}(-\d{4})?$/, 'Invalid postal code'),
  }),
});

type ClinicInformationRequest = {
  name: string;
  businessTaxId: string;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
  };
};

type ApiErrorResponse = {
  data: {
    message: string;
    errors?: Record<string, string[]>;
  };
};

interface ClinicInformationStepProps {
  onComplete: VoidFunction;
  gpoLogoUrl: string | null;
}
export const ClinicInformationStep = ({
  onComplete,
  gpoLogoUrl,
}: ClinicInformationStepProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    resolver: yupResolver(SCHEMA),
  });
  const [isLoading, seIsLoading] = useState(false);

  const onSubmit = async (data: ClinicInformationRequest) => {
    seIsLoading(true);
    try {
      await post({
        url: `/clinics`,
        body: {
          ...data,
        },
      });

      onComplete();
    } catch (error) {
      const apiError = error as ApiErrorResponse;

      if (apiError?.data?.errors) {
        Object.keys(apiError.data.errors).forEach((errorKey) => {
          const fieldKey = errorKey as keyof ClinicInformationRequest;
          const messages = apiError.data.errors?.[fieldKey];
          if (messages && messages.length > 0) {
            setError(fieldKey, { message: messages[0] });
          }
        });
      } else {
        errorNotification();
      }
    }
    seIsLoading(false);
  };

  return (
    <OnboardingStep
      title="Add Your Clinic Information"
      subTitle="Please provide the clinic details below, including address, contact information, and any other relevant data."
      currentStep={2}
      totalSteps={3}
      gpoLogoUrl={gpoLogoUrl}
    >
      <OnboardingStepFormWrapper onSubmit={handleSubmit(onSubmit)}>
        <Text c="#344054" size="1rem" fw="500" mb="md">
          Clinic Info
        </Text>

        <Box mb="24px">
          <Input
            label="Clinic Name"
            {...register('name')}
            error={errors.name?.message}
          />
        </Box>
        <Box mb="24px">
          <Input
            label="Business Tax ID"
            {...register('businessTaxId')}
            mask={einMask}
            error={errors.businessTaxId?.message}
          />
        </Box>
        <Box>
          <Input
            label="Phone Number"
            {...register('phoneNumber')}
            mask={phoneMask}
            error={errors.phoneNumber?.message}
          />
        </Box>

        <Divider my="2rem" />

        <Box mb="24px">
          <Input
            label="Street"
            {...register('address.street')}
            error={errors.address?.street?.message}
          />
        </Box>
        <Flex gap="1rem" mb="24px">
          <Box flex="1">
            <Input
              label="City"
              {...register('address.city')}
              error={errors.address?.city?.message}
            />
          </Box>
          <Box flex="1">
            <Select
              label="State"
              {...register('address.state')}
              error={errors.address?.state?.message}
              options={US_STATES}
            />
          </Box>
        </Flex>
        <Box mb="24px">
          <Input
            label="Postal Code"
            {...register('address.postalCode')}
            mask={postalCodeMask}
            error={errors.address?.postalCode?.message}
          />
        </Box>
      </OnboardingStepFormWrapper>
      <Box mt="2rem" w="100%">
        <Button type="submit" form="stepForm" loading={isLoading}>
          Finish
        </Button>
      </Box>
    </OnboardingStep>
  );
};
