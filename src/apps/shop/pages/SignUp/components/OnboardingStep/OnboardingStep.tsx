import { Logo } from '@/libs/ui/Logo/Logo';
import { Box, Divider, Flex, Text, Title } from '@mantine/core';
import { ReactNode } from 'react';

interface OnboardingStepProps {
  title: string | ReactNode;
  subTitle: string;
  currentStep: number;
  totalSteps: number;
  children: ReactNode;
  gpoLogoUrl: string | null;
}
export const OnboardingStep = ({
  title,
  subTitle,
  currentStep,
  totalSteps,
  children,
  gpoLogoUrl,
}: OnboardingStepProps) => {
  return (
    <Flex w="100%" mih="100vh" align="center" direction="column" my="4rem">
      <Flex
        direction="column"
        align="center"
        w="540px"
        bg="#FFF"
        style={{
          border: '1px solid rgba(208, 213, 221, 0.35)',
          borderRadius: '8px',
        }}
        px="32px"
        py="48px"
      >
        <Box w="212px">
          {gpoLogoUrl ? <img src={gpoLogoUrl} width="100%" /> : <Logo />}
        </Box>
        <Divider w="100%" my="2rem" />
        <Text ta="center" fw="500" size="12px" c="#666" mb="4px">
          STEP {currentStep}/{totalSteps}
        </Text>
        <Title order={3} mb="8px" ta="center" fw="500" lh="normal">
          {title}
        </Title>
        <Text
          ta="center"
          size="14px"
          lh="normal"
          c="rgba(0, 0, 0, 0.80)"
          mb="4px"
          maw="340px"
        >
          {subTitle}
        </Text>
        {children}
      </Flex>
      {gpoLogoUrl ? (
        <Flex align="center" gap="0.5rem" mt="1rem" mb="2.5rem">
          <Text>Powered by</Text>
          <Box w="112px">
            <Logo type="full" />
          </Box>
        </Flex>
      ) : null}
    </Flex>
  );
};
