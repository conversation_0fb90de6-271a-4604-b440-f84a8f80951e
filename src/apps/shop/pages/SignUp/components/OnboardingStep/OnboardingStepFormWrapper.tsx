import { Box } from '@mantine/core';
import { ReactNode } from 'react';

interface OnboardingStepFormWrapperProps {
  children: ReactNode;
  onSubmit: VoidFunction;
}
export const OnboardingStepFormWrapper = ({
  children,
  onSubmit,
}: OnboardingStepFormWrapperProps) => {
  return (
    <Box
      w="100%"
      bg="#F2F8FC"
      mt="32px"
      p="24px"
      style={{
        border: '1px solid rgba(208, 213, 221, 0.35)',
        borderRadius: '8px',
      }}
    >
      <form onSubmit={onSubmit} id="stepForm">
        {children}
      </form>
    </Box>
  );
};
