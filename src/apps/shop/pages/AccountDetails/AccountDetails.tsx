import { useEffect } from 'react';
import { Resolver, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'react-i18next';

import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';

import type { FormValues } from './types';
import { SCHEMA } from './constants';
import { Box, Divider, Flex } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import { Input } from '@/libs/form/Input';
import { einMask } from '@/libs/form/masks';

export const AccountDetails = () => {
  const { t } = useTranslation();

  const { updateAccount, account } = useAccountStore();

  const { register, handleSubmit, reset, formState, setError } =
    useForm<FormValues>({
      resolver: yupResolver(SCHEMA) as unknown as Resolver<FormValues>,
      mode: 'all',
      reValidateMode: 'onChange',
    });

  useEffect(() => {
    if (account) {
      reset({
        name: account.name,
        ein: account.ein,
        phoneNumber: account.phoneNumber,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [account]);

  const { apiRequest: handleSave, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      const response = await post<FormValues>({
        url: `/accounts/${account?.id}`,
        body: values,
        method: 'PATCH',
      });

      await updateAccount(response);
      successNotification('Account information was updated');
    }),
    // TODO: Analyze server error handling
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  const errors = formState.errors;

  return (
    <form onSubmit={handleSave}>
      <Divider my="md" />
      <Flex direction="column" gap="md">
        <Flex gap="1rem">
          <Input
            id="name"
            label={t('onboarding.step1.accountName')}
            error={errors.name?.message}
            size="lg"
            {...register('name')}
          />
          <Input
            id="ein"
            label={t('onboarding.step1.ein')}
            mask={einMask}
            error={errors.ein?.message}
            size="lg"
            {...register('ein')}
          />
        </Flex>
        <Flex gap="1rem">
          <Input
            id="phoneNumber"
            label={t('onboarding.step1.phoneNumber')}
            maxLength={15}
            error={errors.phoneNumber?.message}
            type="tel"
            size="lg"
            {...register('phoneNumber')}
          />
        </Flex>
        <Box mt="0.5rem">
          <Button
            variant="secondary"
            loading={isLoading}
            disabled={!formState.isValid}
          >
            {t('common.save')}
          </Button>
        </Box>
      </Flex>
    </form>
  );
};
