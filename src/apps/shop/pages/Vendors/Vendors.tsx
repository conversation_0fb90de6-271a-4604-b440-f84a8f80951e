import { useEffect } from 'react';
import { Box, LoadingOverlay, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';

import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { VendorConnectModal } from './components/VendorConnectModal/VendorConnectModal';
import { VendorItem } from './components/VendorItem/VendorItem';
import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { VendorType } from '@/types';

export const Vendors = () => {
  const { vendors, getVendors: getVendorsRequest } = useVendorsStore();
  const { account } = useAccountStore();

  const { apiRequest: getVendors, isLoading } = useAsyncRequest({
    apiFunc: getVendorsRequest,
  });

  useEffect(() => {
    getVendors();
  }, [getVendors]);

  const preferedVendorOrderMapState = (
    account?.gpo?.preferredVendors ?? []
  ).reduce<Record<string, number>>(
    (acc, { vendorId, order }) => ({
      ...acc,
      [vendorId]: order,
    }),
    {},
  );

  const sortedVendors = [...vendors].sort(
    (vendor1: VendorType, vendor2: VendorType) => {
      const vendor1Order = preferedVendorOrderMapState[vendor1.id] ?? Infinity;
      const vendor2Order = preferedVendorOrderMapState[vendor2.id] ?? Infinity;

      return vendor1Order - vendor2Order;
    },
  );

  return (
    <div className="mainSection">
      <PageHeader
        title="Vendor Management"
        description="Connect, manage and check connection status"
      />

      {sortedVendors.length ? (
        <Flex direction="column">
          {sortedVendors.map((vendor) => (
            <Box key={vendor.id}>
              <VendorItem
                vendor={vendor}
                isPrefered={
                  preferedVendorOrderMapState[vendor.id] !== undefined
                }
              />
            </Box>
          ))}
        </Flex>
      ) : (
        <Flex align="center" justify="center" mih="300px">
          <Text size="lg">No vendors found</Text>
        </Flex>
      )}

      <LoadingOverlay visible={isLoading} />
      <VendorConnectModal />
    </div>
  );
};
