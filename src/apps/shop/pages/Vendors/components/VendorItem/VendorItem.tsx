import type { VendorType } from '@/types';
import { Box, Divider, Image, Menu, Text, UnstyledButton } from '@mantine/core';
import { MODAL_NAME } from '@/constants';
import { Button, type ButtonProps } from '@/libs/ui/Button/Button';
import { Alert } from '@/libs/ui/Alert/Alert';
import { MarkdownRenderer } from '@/libs/ui/MarkdownRenderer/MarkdownRenderer';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { CatalogSyncStatus } from '../CatalogSyncStatus/CatalogSyncStatus';
import { VendorConnectionStatus } from '../VendorConnectionStatus/VendorConnectionStatus';
import MoreOptionsIcon from './assets/more-options.svg?react';
import styles from './VendorItem.module.css';
import dayjs from 'dayjs';
import { RecommendedTag } from '@/libs/ui/RecommendedTag/RecommendedTag';
import { Flex } from '@/libs/ui/Flex/Flex';

interface VendorItemProps {
  vendor: VendorType;
  isPrefered: boolean;
}
export const VendorItem = ({ vendor, isPrefered }: VendorItemProps) => {
  const { openModal } = useModalStore();

  const handleOpenModal = (vendor: VendorType) => {
    openModal({ name: MODAL_NAME.VENDOR_CONNECT, vendor });
  };

  const {
    id,
    alert,
    imageUrl,
    name,
    lastProductCatalogSync,
    status,
    type,
    integrationPoints,
  } = vendor;

  const connectCTA: {
    show: boolean;
    label: string;
    variant: ButtonProps['variant'];
  } = {
    show:
      lastProductCatalogSync?.status === 'failed' || status === 'disconnected',
    label: lastProductCatalogSync ? 'Update Credentials' : 'Connect',
    variant: lastProductCatalogSync ? 'secondary' : 'default',
  };

  const isMoreOptionsDisabled =
    status === 'connecting' ||
    (status === 'disconnected' && !lastProductCatalogSync);

  const vendorHasNoProductCatalogSync =
    status === 'connected' &&
    !integrationPoints.includes('sync_product_catalog');

  return (
    <Flex
      key={id}
      className={styles.container}
      px="2rem"
      py="1.5rem"
      mb="md"
      align="center"
      justify="space-between"
      wrap="wrap"
    >
      {isPrefered && (
        <RecommendedTag left="2rem" size="xs" placement="top">
          Preferred vendor
        </RecommendedTag>
      )}
      <Flex w="33%" align="center" pos="relative">
        <Image h="2.4rem" src={imageUrl} />
        <Text ml="md" fw="500">
          {name}
        </Text>
      </Flex>
      <Flex w="67%" justify="space-between">
        <Flex>
          <Divider orientation="vertical" mx="2rem" mih="100%" />
          <Box miw="100px">
            <Text c="#666" size="0.75rem" fw="400" mb="0.5rem">
              Category
            </Text>
            <Text c="#333" size="0.875rem" fw="500" tt="capitalize">
              {type}
            </Text>
          </Box>
          <Divider orientation="vertical" mx="2rem" mih="100%" />
          <Box>
            <Text c="#666" size="0.75rem" fw="400" mb="0.5rem">
              Product Catalog
            </Text>
            <Box>
              {lastProductCatalogSync ? (
                <Flex align="center" gap="0.5rem">
                  <CatalogSyncStatus status={lastProductCatalogSync.status} />
                  {lastProductCatalogSync.status === 'succeeded' && (
                    <>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="4"
                        height="4"
                        viewBox="0 0 4 4"
                        fill="none"
                      >
                        <circle cx="2" cy="2" r="2" fill="#D9D9D9" />
                      </svg>
                      <Text size="0.75rem" c="rgba(102, 102, 102, 0.60)">
                        <Text fw="500" span>
                          Last update:
                        </Text>
                        {' ' +
                          dayjs(lastProductCatalogSync.updatedAt).format(
                            'MMMM D, YYYY',
                          )}
                      </Text>
                    </>
                  )}
                </Flex>
              ) : vendorHasNoProductCatalogSync ? (
                <CatalogSyncStatus status="succeeded" />
              ) : (
                '-'
              )}
            </Box>
          </Box>
        </Flex>
        <Flex>
          {connectCTA.show ? (
            <Button
              onClick={() => handleOpenModal(vendor)}
              variant={connectCTA.variant}
            >
              {connectCTA.label}
            </Button>
          ) : (
            <VendorConnectionStatus status={status} />
          )}
          <Menu>
            <Menu.Target>
              <UnstyledButton
                px="md"
                ml="md"
                disabled={isMoreOptionsDisabled}
                opacity={isMoreOptionsDisabled ? 0.4 : 1}
              >
                <MoreOptionsIcon />
              </UnstyledButton>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Label>
                <UnstyledButton onClick={() => handleOpenModal(vendor)}>
                  <Text size="0.875rem" fw="500" c="#333">
                    Edit Credentials
                  </Text>
                </UnstyledButton>
              </Menu.Label>
            </Menu.Dropdown>
          </Menu>
        </Flex>
      </Flex>
      {alert ? (
        <Flex miw="100%" mt="md">
          <Alert type={alert.type}>
            <MarkdownRenderer
              markdown={alert.message}
              className={styles.markdown}
            />
          </Alert>
        </Flex>
      ) : null}
    </Flex>
  );
};
