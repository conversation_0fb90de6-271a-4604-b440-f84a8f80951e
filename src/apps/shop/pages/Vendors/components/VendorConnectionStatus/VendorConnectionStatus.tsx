import type { VendorType } from '@/types';
import { Badge } from '@/libs/ui/Badge/Badge';

interface VendorConnectionStatusProps {
  status: VendorType['status'];
}
export const VendorConnectionStatus = ({
  status,
}: VendorConnectionStatusProps) => {
  const statusConfigs: Record<
    VendorType['status'],
    {
      background: string;
      color: string;
    } | null
  > = {
    connected: {
      background: '#89BF77',
      color: '#FFF',
    },
    connecting: {
      background: '#B6F5F9',
      color: '#344054',
    },
    disconnected: null,
  };

  const config = statusConfigs[status];

  if (config) {
    return <Badge {...config}>{status}</Badge>;
  }

  return null;
};
