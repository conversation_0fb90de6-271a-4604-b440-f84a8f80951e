import { useForm } from 'react-hook-form';
import * as Yup from 'yup';
import i18n from '@/apps/shop/i18n';

import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { yupResolver } from '@hookform/resolvers/yup';

import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { Button } from '@/libs/ui/Button/Button';
import { Input } from '@/libs/form/Input';
import { Box } from '@mantine/core';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

export interface AmazonVendorConnectForm {
  buyingGroupId: string;
}

const SCHEMA = Yup.object().shape({
  buyingGroupId: Yup.string()
    .trim()
    .required(
      i18n.t('form.errorMessage.required', {
        path: 'From Identity ',
      }),
    ),
});

interface AmazonVendorConnectModalFormProps {
  buttonLabel: string;
}
export const AmazonVendorConnectModalForm = ({
  buttonLabel,
}: AmazonVendorConnectModalFormProps) => {
  const { modalOption } = useModalStore();
  const { connectAmazonVendor } = useVendorsStore();
  const { vendor } = modalOption as VendorConnectModalOptions;
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<AmazonVendorConnectForm>({
    resolver: yupResolver(SCHEMA),
  });

  const { apiRequest: handleConnect, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      const { redirectUri } = await connectAmazonVendor({
        vendorId: vendor.id,
        ...values,
      });

      window.location.href = redirectUri;
      // TODO: Handle error
      reset();
    }),
  });

  if (!vendor) {
    return null;
  }

  return (
    <Box component="form" onSubmit={handleConnect} w="100%">
      <Box mb="md">
        <Input
          label="From Identity"
          {...register('buyingGroupId')}
          error={errors.buyingGroupId?.message}
        />
      </Box>
      <Button loading={isLoading}>{buttonLabel}</Button>
    </Box>
  );
};
