import { ROUTERS_PATH } from '@/apps/shop/routes/routes';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { Navigate, useLocation } from 'react-router-dom';

// TODO: This should not be a page, this logic should be moved to another type of resource
export const Home = () => {
  const account = useAccountStore();
  const { pathname } = useLocation();
  const publicPaths: string[] = [ROUTERS_PATH.login, ROUTERS_PATH.signUp];

  if (account) {
    return <Navigate to={ROUTERS_PATH.clinicManagement} />;
  }

  if (publicPaths.includes(pathname)) {
    return <Navigate to={pathname} />;
  }

  return <Navigate to={ROUTERS_PATH.login} />;
};
