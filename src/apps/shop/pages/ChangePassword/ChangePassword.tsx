import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Text, Loader } from '@mantine/core';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { Button, InputPasswordField } from '@/components';
import { useTranslation, Trans } from 'react-i18next';

import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { post } from '@/libs/utils/api';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';

import { SCHEMA } from './constants';
import layoutStyles from '@/apps/shop/Layouts/AuthLayout/AuthLayout.module.css';
import styles from './ChangePassword.module.css';

interface FormValues {
  password: string;
  confirmPassword: string;
}

export const ChangePassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { control, handleSubmit, setError } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
  });
  const [isValidLink, setIsValidLink] = useState(true);
  const [searchParams] = useSearchParams();
  const userId = searchParams.get('uuid');
  const token = searchParams.get('token');

  const { apiRequest: validateToken, isLoading: isLoadingValidation } =
    useAsyncRequest({
      apiFunc: async () => {
        if (!token || !userId) {
          setIsValidLink(false);
          return;
        }

        await post<{ auth_token: string; userId: string }>({
          url: `/user/${userId}/validate-token`,
          body: { token, userId },
          method: 'PATCH',
        });
      },
      errorFunc: () => {
        setIsValidLink(false);
      },
    });

  const {
    apiRequest: handleChangePassword,
    isLoading: isLoadingChangePassword,
  } = useAsyncRequest({
    apiFunc: handleSubmit(async ({ password }) => {
      await post({
        url: `/user/${userId}/password-change`,
        body: { password, userId },
        method: 'PATCH',
      });

      successNotification(t('changePassword.passwordChanged'));
      navigate(ROUTERS_PATH.login);
    }),
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  useEffect(() => {
    if (validateToken) {
      validateToken();
    }
  }, [validateToken]);

  if (isLoadingValidation) {
    return (
      <div className={styles.validationLoader} data-testid="loader">
        <Loader size="3rem" />
      </div>
    );
  }

  if (!isValidLink) {
    return (
      <div
        className={styles.validationMessageBlock}
        data-testid="validation-message"
      >
        <Text size="md" className={layoutStyles.title}>
          {t('changePassword.wrongTokenLink')}
        </Text>

        <Link to={ROUTERS_PATH.forgotPassword}>
          {t('changePassword.resetPassword')}
        </Link>
      </div>
    );
  }

  return (
    <>
      <Text size="xlLg" className={layoutStyles.title}>
        {t('changePassword.title')}
      </Text>

      <Text size="md" className={layoutStyles.subtitle}>
        <Trans
          i18nKey="changePassword.subtitle"
          components={{ b: <b />, br: <br /> }}
          values={{ email: '' }}
          // TODO update user data
          // values={{ email: '<EMAIL>' }}
        />
      </Text>

      <div className={layoutStyles.form}>
        <InputPasswordField
          name="password"
          label={t('form.field.password')}
          placeholder={t('form.field.password')}
          control={control}
          size="md"
        />

        <InputPasswordField
          name="confirmPassword"
          label={t('form.field.confirmPassword')}
          placeholder={t('form.field.confirmPassword')}
          control={control}
          size="md"
        />

        <Button
          onClick={handleChangePassword}
          mt="3rem"
          loading={isLoadingChangePassword}
          fullWidth
          dataTestId="submit"
        >
          {t('changePassword.confirm')}
        </Button>
      </div>
    </>
  );
};
