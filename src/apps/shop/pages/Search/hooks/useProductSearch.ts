import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { queryKeys } from '@/libs/query/queryClient';
import { get } from '@/libs/utils/api';
import { buildQueryString } from '@/utils/buildQueryString';
import type { SearchParamsProps, ProductType } from '@/types';
import type {
  GetDataWithPagination,
  SortOrderOptionType,
} from '@/types/utility';

interface UseProductSearchOptions {
  enabled?: boolean;
}

interface ProductSearchResult {
  data: ProductType[];
  total: number;
  page: number;
  perPage: string;
  query: string;
  sortBy?: string;
  sortOrder?: string;
  vendorIds?: string;
}

/**
 * Custom hook for product search using TanStack Query
 *
 * Features:
 * - Automatic caching of search results
 * - Background refetching when parameters change
 * - Optimistic updates for pagination
 * - Smart cache invalidation
 * - Built-in loading and error states
 */
export const useProductSearch = (options: UseProductSearchOptions = {}) => {
  const { clinic } = useClinicStore();
  const [searchParams, setSearchParams] = useSearchParams();
  const queryClient = useQueryClient();

  // Extract search parameters from URL
  const searchQuery = searchParams.get('query')?.trim() || '';
  const page = parseInt(searchParams.get('page') || '1', 10);
  const perPage = searchParams.get('perPage') || '12';
  const sortBy = searchParams.get('sortBy') as keyof ProductType | undefined;
  const sortOrder = searchParams.get('sortOrder') as
    | SortOrderOptionType
    | undefined;
  const vendorIds = searchParams.get('vendorIds') || '';

  // Create search parameters object
  const searchParamsObj: SearchParamsProps<ProductType> = {
    query: searchQuery,
    page,
    perPage,
    sortBy,
    sortOrder,
    vendorIds,
    clinicId: clinic?.id || '',
  };

  // Generate query key for caching
  const queryKey = queryKeys.products.search(
    clinic?.id || '',
    JSON.stringify(searchParamsObj),
  );

  // Query function that performs the search
  const queryFn = async (): Promise<ProductSearchResult> => {
    if (!searchQuery || !clinic?.id) {
      throw new Error('Missing search query or clinic ID');
    }

    // Build query string for API call
    const { sortBy, sortOrder, page, perPage, vendorIds, ...rest } =
      searchParamsObj;
    let apiQuery =
      buildQueryString<Partial<SearchParamsProps<ProductType>>>(rest);

    apiQuery += page ? `&page[number]=${page}` : '';
    apiQuery += perPage ? `&page[size]=${perPage}` : '';
    apiQuery += vendorIds ? `&filter[vendorIds]=${vendorIds}` : '';

    const response = await get<GetDataWithPagination<ProductType>>({
      url: `/clinics/${clinic.id}/search?${apiQuery}`,
    });

    return {
      data: response.data,
      total: response.meta.total,
      page,
      perPage,
      query: searchQuery,
      sortBy,
      sortOrder,
      vendorIds,
    };
  };

  // TanStack Query for search results
  const { data, isLoading, error, refetch, isFetching, isStale } = useQuery({
    queryKey,
    queryFn,
    enabled: Boolean(searchQuery && clinic?.id && options.enabled !== false),
  });

  // Helper functions for updating search parameters
  const updateSearchParams = (
    newParams: Partial<SearchParamsProps<ProductType>>,
  ) => {
    const updatedParams = { ...searchParamsObj, ...newParams };
    const queryString =
      buildQueryString<SearchParamsProps<ProductType>>(updatedParams);
    setSearchParams(new URLSearchParams(queryString));
  };

  const changePage = (newPage: number) => {
    updateSearchParams({ page: newPage });
  };

  const changePerPage = (newPerPage: string) => {
    updateSearchParams({ perPage: newPerPage, page: 1 });
  };

  const changeSort = (
    sortBy?: keyof ProductType,
    sortOrder?: SortOrderOptionType,
  ) => {
    updateSearchParams({ sortBy, sortOrder, page: 1 });
  };

  const changeFilters = (
    filters: Partial<Pick<SearchParamsProps<ProductType>, 'vendorIds'>>,
  ) => {
    updateSearchParams({ ...filters, page: 1 });
  };

  const performSearch = (newQuery: string) => {
    updateSearchParams({ query: newQuery.trim(), page: 1 });
  };

  // Cache management utilities
  const clearSearchCache = () => {
    if (clinic?.id) {
      queryClient.removeQueries({
        predicate: (query) => {
          const key = query.queryKey;
          return (
            key[0] === 'products' && key[1] === 'search' && key[2] === clinic.id
          );
        },
      });
    }
  };

  const prefetchNextPage = async () => {
    if (data && clinic?.id) {
      const nextPage = page + 1;
      const totalPages = Math.ceil(data.total / parseInt(perPage, 10));

      if (nextPage <= totalPages) {
        const nextPageParams = { ...searchParamsObj, page: nextPage };
        const nextPageKey = queryKeys.products.search(
          clinic.id,
          JSON.stringify(nextPageParams),
        );

        await queryClient.prefetchQuery({
          queryKey: nextPageKey,
          queryFn: () => {
            // Similar query function but for next page
            const {
              sortBy,
              sortOrder,
              page: nextPageNum,
              perPage,
              vendorIds,
              ...rest
            } = nextPageParams;
            let apiQuery =
              buildQueryString<Partial<SearchParamsProps<ProductType>>>(rest);

            apiQuery += nextPageNum ? `&page[number]=${nextPageNum}` : '';
            apiQuery += perPage ? `&page[size]=${perPage}` : '';
            apiQuery += vendorIds ? `&filter[vendorIds]=${vendorIds}` : '';

            return get<GetDataWithPagination<ProductType>>({
              url: `/clinics/${clinic.id}/search?${apiQuery}`,
            }).then((response) => ({
              data: response.data,
              total: response.meta.total,
              page: nextPageNum,
              perPage,
              query: searchQuery,
              sortBy,
              sortOrder,
              vendorIds,
            }));
          },
          staleTime: 5 * 60 * 1000,
        });
      }
    }
  };

  return {
    // Search results and state
    productList: data?.data || [],
    total: data?.total || 0,
    page: data?.page || 1,
    perPage: data?.perPage || '12',
    query: searchQuery,
    sortBy: data?.sortBy,
    sortOrder: data?.sortOrder,
    vendorIds: data?.vendorIds || '',

    // Loading and error states
    isLoading,
    isFetching,
    isStale,
    hasError: !!error,
    error,

    // Actions
    changePage,
    changePerPage,
    changeSort,
    changeFilters,
    performSearch,
    refetch,

    // Cache management
    clearSearchCache,
    prefetchNextPage,

    // Raw search parameters for compatibility
    searchParams: searchParamsObj,
  };
};
