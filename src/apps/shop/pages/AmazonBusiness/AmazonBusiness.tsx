import { useNavigate } from 'react-router-dom';
import { An<PERSON>, Divider, Flex, Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';

import styles from './layout.module.css';

import { Button } from '@/components';
import { getActiveClinic } from '@/libs/clinics/utils/activeClinic';
import { Logo } from '@/libs/ui/Logo/Logo';

const BASE_URL = import.meta.env.VITE_API_URL || 'https://127.0.0.1:8000';

export const AmazonBusiness = () => {
  const { t } = useTranslation();
  const activeClinic = getActiveClinic();
  const navigate = useNavigate();

  const handleClickExistingAccount = async () => {
    const queryParameters = new URLSearchParams(window.location.search);
    const amazonCallbackUri = queryParameters.get('amazon_callback_uri');
    const amazonState = queryParameters.get('amazon_state');

    if (!amazonCallbackUri || !amazonState) {
      navigate('/not-found');
      return;
    }

    const amazonParams =
      'amazon_state=' +
      amazonState +
      '&state=' +
      activeClinic?.id +
      '&redirect_uri=' +
      `${BASE_URL}/api/oauth2/amazon-business`;

    if (activeClinic?.id) {
      window.location.href =
        amazonCallbackUri +
        '?' +
        amazonParams +
        '&status=authentication_successful';
    } else {
      window.location.href =
        '/' + '?amazon_callback_uri=' + amazonCallbackUri + '&' + amazonParams;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <Flex mt="7rem" maw="300px" justify="center" mx="auto">
          <Logo />
        </Flex>

        <Text className={styles.mainTitle} component="h1">
          {t('onboarding.amazonBusiness.title')}
        </Text>

        <Text className={styles.secondaryTitle}>
          {t('onboarding.amazonBusiness.subtitle')}
        </Text>

        <Text className={styles.commonText}>
          {t('onboarding.amazonBusiness.tagline')}
        </Text>

        <Divider />

        <Text className={styles.connect}>
          {t('onboarding.amazonBusiness.connect')}
        </Text>

        <div className={styles.buttonGroup}>
          <Button onClick={handleClickExistingAccount}>
            {t('onboarding.amazonBusiness.existingAccount')}
          </Button>
          <Button
            onClick={() =>
              (window.location.href = 'https://www.highfive.vet/onboarding')
            }
          >
            {t('onboarding.amazonBusiness.newAccount')}
          </Button>
        </div>
      </div>
      <div className={styles.footer}>
        <Anchor
          className={styles.footerText}
          href="tel:+**********"
          target="_blank"
        >
          Tel #: {t('common.telNumber')}
        </Anchor>
      </div>
    </div>
  );
};
