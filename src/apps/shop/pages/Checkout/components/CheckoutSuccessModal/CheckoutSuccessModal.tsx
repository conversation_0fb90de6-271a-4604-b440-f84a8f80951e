import dayjs from 'dayjs';
import { Box, Divider, Flex, Image, Text } from '@mantine/core';

import { DEFAULT_DISPLAY_DATE_FORMAT, MODAL_NAME } from '@/constants';
import {
  ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { Modal } from '@/components';
import type { CheckoutResponseType } from '@/apps/shop/stores/useCartStore/type';
import bigCheckImage from './assets/big-check.png';
import { Link, useNavigate } from 'react-router-dom';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';
import { getOrderHistoryItemUrl } from '@/apps/shop/routes/utils';
import { Button } from '@/libs/ui/Button/Button';
import styles from './CheckoutSuccessModal.module.css';
import { useEffect } from 'react';
import { getPriceString } from '@/utils';

export const CheckoutSuccessModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const navigate = useNavigate();

  const { id, orderNumber, orderDate, numberOfVendors, totalItems, vendors } =
    modalOption as CheckoutResponseType & ModalOptionProps;

  const orderTotal = vendors?.reduce(
    (acc, { orderTotal }) => acc + orderTotal,
    0,
  );

  const handleModalClose = () => {
    navigate(ROUTERS_PATH.home);
  };

  useEffect(() => {
    return () => {
      closeModal();
    };
  }, [closeModal]);

  return (
    <Modal
      name={MODAL_NAME.CHECKOUT_SUCCESS}
      onClose={handleModalClose}
      closeOnClickOutside={false}
      closeOnEscape={false}
    >
      <Flex direction="column" align="center">
        <Image src={bigCheckImage} w="4rem" />
        <Text size="1.5rem" fw="700" my="md" c="#333">
          Order Placed!
        </Text>
        <Text size="0.875rem" ta="center" c="#666">
          Your order{' '}
          <Text span c="#333" fw="700">
            #{orderNumber}
          </Text>{' '}
          was placed successfully.
        </Text>
        <Box mt="md" p="md" className={styles.generalInfo}>
          <Flex justify="space-between" w="100%">
            <Box>
              <Text>Order ID</Text>
              <Text fw="500">#{orderNumber}</Text>
            </Box>
            <Box>
              <Text ta="right">Order Total</Text>
              <Text ta="right" fw="500">
                {getPriceString(orderTotal)}
              </Text>
            </Box>
          </Flex>
          <Divider my="md" />
          <Flex justify="space-between">
            <Text size="0.75rem" c="#666">
              Date:{' '}
              <Text fw="700" c="#333" span>
                {dayjs(orderDate).format(DEFAULT_DISPLAY_DATE_FORMAT)}
              </Text>
            </Text>
            <Text size="0.75rem" c="#666">
              Total Items:{' '}
              <Text fw="700" c="#333" span>
                {totalItems}
              </Text>
            </Text>
            <Text size="0.75rem" c="#666">
              Total Vendors:{' '}
              <Text fw="700" c="#333" span>
                {numberOfVendors}
              </Text>
            </Text>
          </Flex>
        </Box>
        <Box w="100%" mb="md">
          <Flex align="center" my="md">
            <Divider w="100%" label="Order Summary" labelPosition="left" />
          </Flex>
          {vendors?.map(
            ({ vendor, orderTotal: vendorTotal, totalItems }, vendorIndex) => (
              <Flex
                key={vendor.id}
                w="100%"
                justify="space-between"
                wrap="wrap"
              >
                {vendorIndex !== 0 ? <Divider w="100%" my="10px" /> : null}
                <Text size="0.875rem" fw="700">
                  {vendor.name}
                </Text>
                <Text size="0.75rem" c="#666">
                  Total spend:{' '}
                  <Text fw="700" c="#333" span>
                    {getPriceString(vendorTotal)}
                  </Text>
                </Text>
                <Text size="0.75rem" c="#666">
                  Total of items:{' '}
                  <Text fw="700" c="#333" span>
                    {totalItems}
                  </Text>
                </Text>
              </Flex>
            ),
          )}
        </Box>
        <Divider variant="dashed" w="100%" my="xs" />
        <Flex justify="space-between" align="center" w="100%" mt="md">
          <Link
            to={ROUTERS_PATH.clinicDashboard}
            className={styles.backToHomeLink}
          >
            Back to Homepage
          </Link>
          <Box maw="212px">
            <Button to={getOrderHistoryItemUrl(id)} variant="secondary">
              See order details
            </Button>
          </Box>
        </Flex>
      </Flex>
    </Modal>
  );
};
