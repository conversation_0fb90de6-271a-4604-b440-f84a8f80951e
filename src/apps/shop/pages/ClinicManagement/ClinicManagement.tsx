import { useTranslation } from 'react-i18next';
import { Table } from '@/components';
import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import styles from './ClinicManagement.module.css';
import clsx from 'clsx';
import { useMemo, useState } from 'react';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { Navigate, useNavigate } from 'react-router-dom';
import { ClinicType } from '@/types/common.ts';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';
import { ColumnDef } from '@tanstack/react-table';
import { Text } from '@mantine/core';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { getColumns } from './columns';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';

export const ClinicManagement = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { fetchCart } = useCartStore();

  const { user } = useAuthStore();
  const { account, setActiveClinic } = useAccountStore();
  const { clinics = [] } = account || {};

  const [searchData] = useState<{
    data: ClinicType[];
    query: string;
  }>({
    data: clinics,
    query: '',
  });

  const { apiRequest: getSelectedClinic, isLoading: isLoadingClinic } =
    useAsyncRequest({
      apiFunc: async (data?: ClinicType) => {
        if (!data) {
          return;
        }

        await fetchCart();
        await setActiveClinic({
          id: data.id,
          name: data.name,
          status: data.onboardingStatus,
          hasAnyVendorConnected: data.hasAnyVendorConnected,
        });

        if (data.hasAnyVendorConnected) {
          navigate(ROUTERS_PATH.clinicDashboard);
        } else {
          navigate(ROUTERS_PATH.vendors);
        }
      },
    });

  const columns = useMemo(
    () => getColumns(getSelectedClinic as (data?: ClinicType) => Promise<void>),
    [getSelectedClinic],
  ) as ColumnDef<ClinicType>[];

  if (user?.account.clinics.length === 0) {
    return <Navigate to={ROUTERS_PATH.signUp} />;
  }

  return (
    <div className="mainSection">
      <PageHeader title={t('sidebar.clinicManagement')} />

      <div className={clsx(styles.pageContentRoot, 'pageContent')}>
        <Table
          isLoading={isLoadingClinic}
          data={searchData.data}
          columns={columns}
          paddingCellWidth="1rem"
          emptyContent={
            <Text fw="700" m="1rem auto" ta="center">
              {t('client.common.emptyTable')}
            </Text>
          }
        />
      </div>
    </div>
  );
};
