import { MouseEvent, startTransition } from 'react';

import { ColumnDef, createColumnHelper } from '@tanstack/react-table';

import i18n from '@/apps/shop/i18n';
import { TruncatedTextWithTooltip } from '@/components';
import { ColorBadge } from '@/libs/ui/ColorBadge/ColorBadge';
import { getClinicVendorStatusParams } from '@/libs/ui/ColorBadge/utils';
import { ClinicType } from '@/types/common.ts';

const columnHelper = createColumnHelper<ClinicType>();

export const getColumns = (
  getData: (clinic?: ClinicType) => Promise<void>,
): ColumnDef<ClinicType>[] =>
  [
    columnHelper.accessor('name', {
      header: i18n.t('superAdmin.dashboard.columns.name'),
      cell: (info) => {
        const get = async () => {
          await getData(info.row.original);
        };
        // to avoid setting 2 records in history
        const handleClick = async (event: MouseEvent<HTMLSpanElement>) => {
          event.preventDefault();
          event.stopPropagation();

          startTransition(get as () => void);
        };

        const name = info.getValue();

        return (
          <TruncatedTextWithTooltip
            text={name}
            onClick={handleClick}
            data-hover="true"
            size="sm"
            fw={700}
          />
        );
      },
      maxSize: 300,
    }),
    // TODO add value manager
    columnHelper.accessor('shippingAddress', {
      header: i18n.t('client.clinicManagement.columns.manager'),
      cell: () => {
        return '--';
      },
    }),
    // TODO add value email
    columnHelper.accessor('billingAddress', {
      header: i18n.t('client.clinicManagement.columns.email'),
      cell: () => {
        return '--';
      },
    }),
    columnHelper.accessor('hasAnyVendorConnected', {
      header: i18n.t('client.clinicManagement.columns.status'),
      cell: (info) => {
        return (
          // Last place with ColorBadge component
          <ColorBadge
            value={info.getValue() ? 'connected' : 'disconnected'}
            paramsMapper={getClinicVendorStatusParams}
          />
        );
      },
    }),
  ] as unknown as ColumnDef<ClinicType>[];
