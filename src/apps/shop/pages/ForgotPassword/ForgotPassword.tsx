import { Text } from '@mantine/core';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useTranslation } from 'react-i18next';

import { <PERSON><PERSON>, InputField } from '@/components';

import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';

import { SCHEMA } from './constants';
import styles from '@/apps/shop/Layouts/AuthLayout/AuthLayout.module.css';

interface FormValues {
  email: string;
}

export const ForgotPassword = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { control, handleSubmit, setError } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
  });

  const { apiRequest: handleResetPassword, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      await post({
        url: '/password-resets',
        body: values,
      });

      successNotification(t('forgotPassword.resetMessage'));
      navigate(ROUTERS_PATH.login);
    }),
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  return (
    <>
      <Text size="xlLg" className={styles.title}>
        {t('forgotPassword.title')}
      </Text>

      <Text size="md" className={styles.subtitle}>
        {t('forgotPassword.subtitle')}
      </Text>

      <div className={styles.form}>
        <InputField
          name="email"
          label={t('form.field.email')}
          placeholder={t('form.field.email')}
          control={control}
          size="md"
        />

        <Button
          onClick={handleResetPassword}
          loading={isLoading}
          size="md"
          fullWidth
        >
          {t('forgotPassword.submit')}
        </Button>
      </div>

      <div className={styles.footer}>
        {t('forgotPassword.haveAccount')}

        <Link to={ROUTERS_PATH.login}>{t('forgotPassword.logIn')}</Link>
      </div>
    </>
  );
};
