import { useState } from 'react';
import { Budget } from './components/Budget/Budget';
import { ClinicInfo } from './components/ClinicInfo/ClinicInfo';
import { SettingsTabs } from './components/SeetingsTabs/SeetingsTabs';
import { Box, Divider, Flex, Text, UnstyledButton } from '@mantine/core';
import { Practices } from './components/Practices/Practices';
// import { VendorsSettings } from './components/VendorsSettings/VendorsSettings';
import { useSearchParams } from 'react-router-dom';

const tabIndexes = ['clinic', 'budget' /* 'vendors' ,*/, 'practices'];

export const Settings = () => {
  const [searchParams] = useSearchParams();
  const tabIndex = tabIndexes.indexOf(searchParams.get('tab') ?? '');
  const [activeTab, setActiveTab] = useState(tabIndex > 0 ? tabIndex : 0);
  const [isEditing, setIsEditing] = useState(false);

  const handleClick = (index: number) => {
    setActiveTab(index);
    setIsEditing(false);
  };

  const tabs = [
    {
      label: 'Clinic Info',
      title: 'Clinic Info Settings',
      onClick: handleClick,
      component: (
        <ClinicInfo
          isEditing={isEditing}
          onComplete={() => setIsEditing(false)}
        />
      ),
    },
    {
      label: 'Budget',
      title: 'Budget Settings',
      onClick: handleClick,
      component: (
        <Budget isEditing={isEditing} onComplete={() => setIsEditing(false)} />
      ),
    },
    // {
    //   label: 'Vendors',
    //   title: 'Preferred Vendors',
    //   onClick: handleClick,
    //   component: (
    //     <VendorsSettings
    //       isEditing={isEditing}
    //       onComplete={() => setIsEditing(false)}
    //       startEditing={() => setIsEditing(true)}
    //     />
    //   ),
    // },
    {
      label: 'Practices',
      title: 'Practices Settings',
      onClick: handleClick,
      component: (
        <Practices
          isEditing={isEditing}
          startEditing={() => setIsEditing(true)}
          onComplete={() => setIsEditing(false)}
        />
      ),
    },
  ];

  const currentTab = tabs[activeTab];

  return (
    <>
      <Box mt="12px">
        <SettingsTabs activeTab={activeTab} tabs={tabs} />
      </Box>
      <Divider my="md" />

      {isEditing ? null : (
        <Flex justify="space-between" align="center">
          <Text c="#344054" size="14px">
            {currentTab.title}
          </Text>
          <UnstyledButton onClick={() => setIsEditing(true)}>
            <Text c="#0072C6" fw="500" size="12px" span>
              Edit {currentTab.label}
            </Text>
          </UnstyledButton>
        </Flex>
      )}
      {currentTab.component}
    </>
  );
};
