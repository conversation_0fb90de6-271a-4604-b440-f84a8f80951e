import * as Yup from 'yup';
import i18n from '@/apps/shop/i18n';
import { BUDGET_TYPE } from '@/libs/cart/constants';
import { budgetNumberValidation } from './utils';

export const SCHEMA = Yup.object().shape({
  type: Yup.string()
    .oneOf(Object.values(BUDGET_TYPE))
    .required(
      i18n.t('form.errorMessage.required', {
        path: i18n.t('client.settings.budget.budgetType'),
      }),
    ),
  weeklyCogs: budgetNumberValidation(BUDGET_TYPE.STATIC, 'weeklyCOGS'),
  monthlyCogs: budgetNumberValidation(BUDGET_TYPE.STATIC, 'monthlyCOGS'),
  weeklyGa: budgetNumberValidation(BUDGET_TYPE.STATIC, 'weeklyGA'),
  monthlyGa: budgetNumberValidation(BUDGET_TYPE.STATIC, 'monthlyGA'),
  targetCogsPercent: budgetNumberValidation(BUDGET_TYPE.DYNAMIC, 'COGSTarget'),
  targetGaPercent: budgetNumberValidation(BUDGET_TYPE.DYNAMIC, 'GATarget'),
  avgTwoWeeksSales: budgetNumberValidation(
    BUDGET_TYPE.DYNAMIC,
    'avgTwoWeeksSales',
  ),
  monthToDateSales: budgetNumberValidation(
    BUDGET_TYPE.DYNAMIC,
    'monthToDateSales',
  ),
});
