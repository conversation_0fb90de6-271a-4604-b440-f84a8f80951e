import i18n from '@/apps/shop/i18n';
import * as Yup from 'yup';
import { BUDGET_TYPE } from '@/libs/cart/constants';
import { FormValues } from '../../interfaces';
import { BudgetType } from '@/libs/cart/types';

type BudgetTypeValue = keyof typeof BUDGET_TYPE;

export const budgetNumberValidation = (
  budgetStrategy: BudgetTypeValue,
  field: string,
) => {
  const path = i18n.t(`client.settings.budget.${field}`);

  return Yup.number()
    .transform((value, originalValue) => {
      if (typeof originalValue === 'string') {
        const cleaned = originalValue.replace(/[^0-9.-]+/g, '');
        return Number(cleaned);
      }
      return value;
    })
    .when('type', {
      is: (value: BudgetTypeValue) => value === budgetStrategy,
      then: (schema) =>
        schema
          .min(1)
          .integer(i18n.t('form.errorMessage.integer', { path }))
          .positive(i18n.t('form.errorMessage.positive', { path }))
          .required(i18n.t('form.errorMessage.required', { path })),
      otherwise: (schema) => schema.optional().nullable(),
    });
};

const clearIntegerValue = (value: number | string) => {
  const integerValue = +value?.toString()?.replace(/\D/g, '');
  return integerValue ? integerValue : 0;
};

export function transformValueToSend({
  avgTwoWeeksSales,
  includeExternalData,
  monthToDateSales,
  targetCogsPercent,
  targetGaPercent,
  monthlyCogs,
  externalMonthlyCogs,
  monthlyGa,
  externalWeeklyCogs,
  weeklyCogs,
  weeklyGa,
  type,
}: FormValues): BudgetType {
  return {
    type,
    avgTwoWeeksSales: clearIntegerValue(avgTwoWeeksSales),
    monthToDateSales: clearIntegerValue(monthToDateSales),
    monthlyCogs: clearIntegerValue(monthlyCogs),
    monthlyGa: clearIntegerValue(monthlyGa),
    targetCogsPercent: targetCogsPercent
      ? +(targetCogsPercent / 100).toFixed(2)
      : 0,
    targetGaPercent: targetGaPercent ? +(targetGaPercent / 100).toFixed(2) : 0,
    weeklyCogs: clearIntegerValue(weeklyCogs),
    weeklyGa: clearIntegerValue(weeklyGa),
    includeExternalData: includeExternalData,
    externalMonthlyCogs: clearIntegerValue(externalMonthlyCogs),
    externalWeeklyCogs: clearIntegerValue(externalWeeklyCogs),
  };
}

export function transformValueToForm({
  avgTwoWeeksSales,
  includeExternalData,
  monthToDateSales,
  targetCogsPercent,
  targetGaPercent,
  monthlyCogs,
  externalMonthlyCogs,
  monthlyGa,
  externalWeeklyCogs,
  weeklyCogs,
  weeklyGa,
  type,
}: BudgetType): FormValues {
  return {
    type,
    avgTwoWeeksSales: Math.floor(avgTwoWeeksSales),
    monthToDateSales: Math.floor(monthToDateSales),
    monthlyCogs: Math.floor(monthlyCogs),
    monthlyGa: Math.floor(monthlyGa),
    targetCogsPercent: targetCogsPercent
      ? +(targetCogsPercent * 100).toFixed(2)
      : 0,
    targetGaPercent: targetGaPercent ? +(targetGaPercent * 100).toFixed(2) : 0,
    weeklyCogs: Math.floor(weeklyCogs),
    weeklyGa: Math.floor(weeklyGa),
    includeExternalData,
    externalMonthlyCogs: Math.floor(externalMonthlyCogs),
    externalWeeklyCogs: Math.floor(externalWeeklyCogs),
  };
}
