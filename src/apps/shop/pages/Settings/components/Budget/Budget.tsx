import { Box, Divider, Flex, Text } from '@mantine/core';
import { BudgetForm } from './components/BudgetForm/BudgetForm';

import { BUDGET_TYPE } from '@/libs/cart/constants';
import { useSettingsStore } from '@/apps/shop/stores/useSettingsStore/useSettingsStore';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import styles from './Budget.module.css';
import { useEffect } from 'react';
import { getPriceString } from '@/utils';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import dayjs from 'dayjs';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';

interface BudgetProps {
  isEditing: boolean;
  onComplete: VoidFunction;
}
export const Budget = ({ isEditing, onComplete }: BudgetProps) => {
  const { getBudget, budget } = useSettingsStore();
  const hasExternalData = budget?.includeExternalData;

  const hasStaticType = budget?.type === BUDGET_TYPE.STATIC;

  const { apiRequest: getBudgetApi, isLoading } = useAsyncRequest({
    apiFunc: getBudget,
  });

  useEffect(() => {
    getBudgetApi();
  }, [getBudgetApi]);

  if (isLoading) {
    return (
      <Box pos="relative" p="24px">
        <ContentLoader />
      </Box>
    );
  }

  if (isEditing) {
    return <BudgetForm onComplete={onComplete} />;
  }

  return (
    <>
      <Flex mt="12px">
        <Box flex="1" className={styles.option} p="24px">
          {hasStaticType ? (
            <>
              <Text c="#666" size="14px" mb="14px">
                Budget Type
              </Text>
              <Text c="#344054" size="24px" fw="500">
                Static Budget
              </Text>
              <Divider my="24px" />
              <Text size="14px" c="#666" mb="14px">
                Weekly COGS:{' '}
                <Text c="#333" fw="700" span>
                  {getPriceString(budget?.weeklyCogs)}
                </Text>
              </Text>
              <Text size="14px" c="#666" mb="14px">
                Weekly G&A:{' '}
                <Text c="#333" fw="700" span>
                  {getPriceString(budget?.weeklyGa)}
                </Text>
              </Text>
              <Text size="14px" c="#666" mb="14px">
                Montly COGS:{' '}
                <Text c="#333" fw="700" span>
                  {getPriceString(budget?.monthlyCogs)}
                </Text>
              </Text>
              <Text size="14px" c="#666" mb="14px">
                Montly G&A:{' '}
                <Text c="#333" fw="700" span>
                  {getPriceString(budget?.monthlyGa)}
                </Text>
              </Text>
              {hasStaticType ? (
                <Text size="12px" fw="500" c="rgba(51, 51, 51, 0.40)">
                  Last updated:{' '}
                  {dayjs(budget?.updatedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
                </Text>
              ) : null}
            </>
          ) : (
            <>
              <Text c="#666" size="14px" mb="14px">
                Budget Type
              </Text>
              <Text c="#344054" size="24px" fw="500">
                Dynamic Budget (Based on target %)
              </Text>
              <Divider my="24px" />
              <Text size="14px" c="#666" mb="14px">
                COGS Target %:{' '}
                <Text c="#333" fw="700" span>
                  {budget?.targetCogsPercent}
                </Text>
              </Text>
              <Text size="14px" c="#666" mb="14px">
                G&A Target %:{' '}
                <Text c="#333" fw="700" span>
                  {budget?.targetGaPercent}
                </Text>
              </Text>
              <Text size="14px" c="#666" mb="14px">
                Last Weeks Revenue:{' '}
                <Text c="#333" fw="700" span>
                  {getPriceString(budget?.avgTwoWeeksSales)}
                </Text>
              </Text>
              <Text size="14px" c="#666" mb="14px">
                Month to Date Revenue:{' '}
                <Text c="#333" fw="700" span>
                  {getPriceString(budget?.monthToDateSales)}
                </Text>
              </Text>
              {hasExternalData ? (
                <>
                  <Text size="14px" c="#666" mb="14px">
                    External Weekly COGS:{' '}
                    <Text c="#333" fw="700" span>
                      {getPriceString(budget?.externalWeeklyCogs)}
                    </Text>
                  </Text>
                  <Text size="14px" c="#666" mb="24px">
                    External Monthly COGS:{' '}
                    <Text c="#333" fw="700" span>
                      {getPriceString(budget?.externalMonthlyCogs)}
                    </Text>
                  </Text>
                </>
              ) : null}
              <Text size="12px" fw="500" c="rgba(51, 51, 51, 0.40)">
                Last updated:{' '}
                {dayjs(budget?.updatedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
              </Text>
            </>
          )}
        </Box>
      </Flex>
    </>
  );
};
