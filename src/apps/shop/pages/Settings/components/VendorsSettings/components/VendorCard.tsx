import { Box, Divider, Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import { VendorLiteType } from '../../../types';

interface VendorCardProps {
  vendor: VendorLiteType;
  typeLabel: string;
}
export const VendorCard = ({ vendor, typeLabel }: VendorCardProps) => (
  <Flex
    p="20px"
    bg="#FFF"
    align="center"
    style={{
      borderRadius: '4px',
      border: '1px solid rgba(0, 0, 0, 0.05)',
    }}
  >
    <Flex align="center" miw="340px">
      <Box mr="md">
        <img src={vendor.imageUrl} height="40px" />
      </Box>
      <Box>
        <Text size="12px" c="#344054">
          Vendor -{' '}
          <Text fw="700" c="#3646AC" size="10px" span>
            {typeLabel}
          </Text>
        </Text>
        <Text c="#344054" size="14px" fw="700" mt="8px">
          {vendor.name}
        </Text>
      </Box>
    </Flex>
    <Divider orientation="vertical" mr="30px" />
    <Box>
      <Text c="#344054" size="12px">
        Category
      </Text>
      <Text c="#344054" size="14px" fw="700" mt="8px">
        Distributor
      </Text>
    </Box>
  </Flex>
);
