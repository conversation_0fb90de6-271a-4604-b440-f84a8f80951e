import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { MultiSelect } from '@/libs/form/MultiSelect';
import { Select } from '@/libs/form/Select';
import { Button } from '@/libs/ui/Button/Button';
import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Divider, Text } from '@mantine/core';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { getConnectedVendorOptionsByType } from './utils';
import { defaultFormErrorHandler, successNotification } from '@/utils';
import { post } from '@/libs/utils/api';
import { SCHEMA } from './contants';
import { ContentLoader } from '@/libs/ui/ContentLoader/ContentLoader';

interface VendorsSettingsFormProps {
  clinicId: string;
  onComplete: VoidFunction;
  defaultValues: {
    primaryDistributorId: string;
    secondaryDistributorId: string;
    preferredManufacturerIds: string[];
  };
}
export const VendorsSettingsForm = ({
  clinicId,
  onComplete,
  defaultValues,
}: VendorsSettingsFormProps) => {
  const {
    vendors,
    getVendors,
    isLoading: isVendorsLoading,
  } = useVendorsStore();

  useEffect(() => {
    getVendors();
  }, [getVendors]);

  const { manufactureresOptions, distributorsOptions } =
    getConnectedVendorOptionsByType(vendors);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setError,
  } = useForm({
    resolver: yupResolver(SCHEMA),
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues,
  });

  const { apiRequest: handleUpdateVendorsSettings, isLoading } =
    useAsyncRequest({
      apiFunc: handleSubmit(async (data) => {
        await post({
          url: `/clinics/${clinicId}`,
          method: 'PATCH',
          body: {
            ...data,
          },
        });

        successNotification('Vendor settings was updated');
        onComplete();
      }),
      errorFunc: (error) => {
        defaultFormErrorHandler(error, setError);
      },
    });

  if (isVendorsLoading) {
    return (
      <Box pos="relative" p="24px">
        <ContentLoader />
      </Box>
    );
  }

  return (
    <form onSubmit={handleUpdateVendorsSettings}>
      <Box
        p="24px"
        bg="rgba(0, 0, 0, 0.02)"
        style={{
          border: '1px solid rgba(0, 0, 0, 0.04)',
          borderRadius: '4px',
        }}
      >
        <Box
          p="24px"
          bg="#FFF"
          style={{
            border: '1px solid rgba(0, 0, 0, 0.04)',
            borderRadius: '4px',
          }}
        >
          <Text size="16px" fw="500" c="#344054">
            Select your vendors
          </Text>
          <Divider my="md" />
          <Box mb="md">
            <Select
              options={distributorsOptions}
              label="Primary Distributor"
              {...register('primaryDistributorId')}
              error={errors.primaryDistributorId?.message}
            />
          </Box>
          <Box mb="md">
            <Select
              options={distributorsOptions}
              label="Secondary Distributor"
              error={errors.secondaryDistributorId?.message}
              {...register('secondaryDistributorId')}
            />
          </Box>
          <Box mb="md">
            <MultiSelect
              options={manufactureresOptions}
              label="Preferred Manufactor"
              {...register('preferredManufacturerIds')}
              defaultValue={defaultValues.preferredManufacturerIds}
            />
          </Box>
        </Box>
        <Text size="12px" c="#666" mt="12px">
          Attention, changing this preferences will change the way your offers
          are being display and the way they are being shared with you. Please,
          certify you are doing the right changes.
        </Text>
      </Box>
      <Box mt="24px">
        <Button loading={isLoading} disabled={!isValid} variant="secondary">
          Save
        </Button>
      </Box>
    </form>
  );
};
