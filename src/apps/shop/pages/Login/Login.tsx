import { KeyboardEvent } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Link, useNavigate } from 'react-router-dom';
import { Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';

import { ROUTERS_PATH } from '@/apps/shop/routes/routes';
import {
  InputField,
  InputPasswordField,
  CheckboxField,
  Button,
} from '@/components';
import { post } from '@/libs/utils/api';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler } from '@/utils';
import { UserType } from '@/types/common';

import { SCHEMA } from './constants';
import styles from '@/apps/shop/Layouts/AuthLayout/AuthLayout.module.css';
import { useRememberMe } from './hooks/useRememberMe';

const BASE_URL = import.meta.env.VITE_API_URL || 'https://127.0.0.1:8000';

interface FormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

export const Login = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { control, handleSubmit, reset, setError } = useForm<FormValues>({
    resolver: yupResolver(SCHEMA),
  });
  const { setRememberMeValues } = useRememberMe({
    onLoadValues: reset,
  });

  const handleKeyPress = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleLogin();
    }
  };

  const { apiRequest: handleLogin, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      const { email, password } = values;

      const userData = await post<
        UserType & { clinicId: string | null; clinic_id: string | null }
      >({
        url: '/sessions',
        body: { email, password },
        withApi: false,
      });

      setRememberMeValues(values);

      const queryParameters = new URLSearchParams(window.location.search);

      // TODO: Extract Amazon code from here
      const amazonCallbackUri = queryParameters.get('amazon_callback_uri');
      // TODO: This line should be changed in the future when the BE change the field name from clinic_id to clinicId
      const clinicId = userData.clinicId ?? userData.clinic_id;

      if (amazonCallbackUri && clinicId) {
        const amazonState = queryParameters.get('amazon_state');
        const amazonParams =
          '?amazon_state=' +
          amazonState +
          '&state=' +
          clinicId +
          '&redirect_uri=' +
          `${BASE_URL}/api/oauth2/amazon-business` +
          '&status=authentication_successful';

        window.location.href = amazonCallbackUri + amazonParams;

        return;
      }

      navigate(ROUTERS_PATH.home);
    }),
    errorFunc: (error) => defaultFormErrorHandler(error, setError),
  });

  return (
    <>
      <Text ta="center" size="xlLg" className={styles.title}>
        {t('login.title')}
      </Text>

      <Text ta="center" size="md" className={styles.subtitle}>
        {t('login.subtitle')}
      </Text>

      <div className={styles.form}>
        <InputField
          name="email"
          label={t('form.field.email')}
          placeholder={t('form.field.email')}
          control={control}
          onKeyDown={handleKeyPress}
          disabled={isLoading}
          size="md"
        />

        <InputPasswordField
          name="password"
          label={t('form.field.password')}
          placeholder={t('form.field.password')}
          control={control}
          onKeyDown={handleKeyPress}
          disabled={isLoading}
          size="md"
        />

        <div className={styles.info}>
          <CheckboxField
            name="rememberMe"
            label={t('login.rememberMe')}
            control={control}
          />

          <Link to={ROUTERS_PATH.forgotPassword}>
            {t('login.forgotPassword')}
          </Link>
        </div>

        <Button
          onClick={handleLogin}
          mt="3rem"
          loading={isLoading}
          dataTestId="submit"
          fullWidth
        >
          {t('login.signIn')}
        </Button>
      </div>
    </>
  );
};
