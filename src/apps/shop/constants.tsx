import i18n from '@/apps/shop/i18n';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';

import HomeIcon from './assets/home.svg?react';
import CartIcon from './assets/cart.svg?react';
import ListIcon from './assets/list.svg?react';
import VendorsIcon from './assets/vendors.svg?react';
import SettingsIcon from './assets/settings.svg?react';

export const CLINIC_NAV_LINKS = [
  {
    label: i18n.t('sidebar.home'),
    icon: <HomeIcon />,
    path: ROUTERS_PATH.clinicDashboard,
  },
  {
    label: i18n.t('sidebar.cart'),
    icon: <CartIcon />,
    path: ROUTERS_PATH.cart,
  },
  {
    label: i18n.t('sidebar.orderHistory'),
    icon: <ListIcon />,
    path: ROUTERS_PATH.orderHistory,
  },
  {
    label: i18n.t('sidebar.vendors'),
    icon: <VendorsIcon />,
    path: ROUTERS_PATH.vendors,
  },
];

export const CLINIC_SETTING_LINKS = [
  {
    label: i18n.t('sidebar.settings'),
    icon: <SettingsIcon />,
    path: `${ROUTERS_PATH.settings}`,
  },
];

export const ACCOUNT_NAV_LINKS = [
  {
    label: i18n.t('sidebar.dashboard'),
    icon: <HomeIcon />,
    path: ROUTERS_PATH.accountDashboard,
  },
  {
    label: i18n.t('sidebar.clinicManagement'),
    icon: <ListIcon />,
    path: ROUTERS_PATH.clinicManagement,
  },
];

export const ACCOUNT_SETTING_LINKS = [
  {
    label: i18n.t('sidebar.accountSettings'),
    icon: <SettingsIcon />,
    path: ROUTERS_PATH.accountDetails,
  },
];
