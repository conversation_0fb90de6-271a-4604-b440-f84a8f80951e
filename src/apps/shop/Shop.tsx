import React from 'react';
import { RouterProvider } from 'react-router-dom';
import { LoadingOverlay, Portal } from '@mantine/core';
import { Notifications } from '@mantine/notifications';

import { ThemeProvider } from '@/providers/ThemeProvider';
import { QueryProvider } from '@/providers/QueryProvider';
import { useBoxLoading } from '@/apps/shop/stores/useBoxLoadingStore';

import router from './routes';

import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/dropzone/styles.css';
import '@mantine/core/styles.css';
import '@/assets/css/fonts.css';
import '@/assets/css/tailwind.css';
import '@/assets/css/index.css';

export const Shop = () => {
  const { isBoxLoading, boxId } = useBoxLoading();

  return (
    <QueryProvider>
      <ThemeProvider>
        <RouterProvider router={router} />
        <Notifications position="top-right" zIndex={1000} />

        {isBoxLoading && (
          <Portal target={boxId}>
            <LoadingOverlay visible />
          </Portal>
        )}
      </ThemeProvider>
    </QueryProvider>
  );
};
