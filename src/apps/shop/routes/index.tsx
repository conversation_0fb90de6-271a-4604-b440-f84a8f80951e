import { lazy, LazyExoticComponent } from 'react';
import { createBrowserRouter, RouteObject } from 'react-router-dom';

import { AuthLayout } from '@/apps/shop/Layouts/AuthLayout/AuthLayout';
import { ClinicLayout } from '@/apps/shop/Layouts/ClinicLayout/ClinicLayout';

import { ROUTERS_PATH } from './routes';
import { NavigationHistoryProvider } from '@/libs/utils/navigation/hooks/useNavigationHistory';
import { AccountLayout } from '@/apps/shop/Layouts/AccountLayout/AccountLayout';
import { Home } from '@/apps/shop/pages/Home/Home';
import { SettingsLayout } from '@/apps/shop/Layouts/SettingsLayout/SettingsLayout';

const pages = [
  'AmazonBusiness',
  'Login',
  'ChangePassword',
  'ForgotPassword',
  'ClinicDashboard',
  'Checkout',
  'Search',
  'ProductDetails',
  'OrderHistory',
  'Cart',
  'Vendors',
  'SignUp',
  'Settings',
  'AccountDetails',
  'Dashboard',
  'Page404',
  'ClinicManagement',
] as const;
type PageName = (typeof pages)[number];

const pageModules: Record<
  PageName,
  LazyExoticComponent<() => JSX.Element>
> = pages.reduce(
  (acc, pageName) => ({
    ...acc,
    [pageName]: lazy(() =>
      import(`@/apps/shop/pages/${pageName}/${pageName}.tsx`).then(
        (pageModule) => ({
          default: pageModule[pageName] as React.FC,
        }),
      ),
    ),
  }),
  {} as Record<PageName, LazyExoticComponent<() => JSX.Element>>,
);

export const routes: RouteObject[] = [
  {
    path: ROUTERS_PATH.home,
    element: <Home />,
  },
  {
    element: <NavigationHistoryProvider />,
    children: [
      {
        path: ROUTERS_PATH.amazonBusiness,
        element: <pageModules.AmazonBusiness />,
      },
      {
        path: ROUTERS_PATH.signUp,
        element: <pageModules.SignUp />,
      },
      {
        // TODO: Check if is logged in or not and redirect to right place
        element: <AuthLayout />,
        children: [
          {
            path: ROUTERS_PATH.login,
            element: <pageModules.Login />,
          },
          {
            path: ROUTERS_PATH.forgotPassword,
            element: <pageModules.ForgotPassword />,
          },
          {
            path: ROUTERS_PATH.changePassword,
            element: <pageModules.ChangePassword />,
          },
        ],
      },
      {
        path: '/',
        element: <ClinicLayout />,
        children: [
          {
            path: ROUTERS_PATH.clinicDashboard,
            element: <pageModules.ClinicDashboard />,
          },
          { path: ROUTERS_PATH.cart, element: <pageModules.Cart /> },
          { path: ROUTERS_PATH.search, element: <pageModules.Search /> },
          {
            path: ROUTERS_PATH.productItem,
            element: <pageModules.ProductDetails />,
          },
          {
            path: ROUTERS_PATH.orderHistory,
            element: <pageModules.OrderHistory />,
          },
          {
            path: ROUTERS_PATH.orderHistoryItem,
            element: <pageModules.OrderHistory />,
          },
          { path: ROUTERS_PATH.vendors, element: <pageModules.Vendors /> },
          {
            path: ROUTERS_PATH.settings,
            element: <SettingsLayout title="Clinic Settings" />,
            children: [
              {
                element: <pageModules.Settings />,
                index: true,
              },
            ],
          },
        ],
      },
      {
        path: '/',
        element: <ClinicLayout showCart={false} />,
        children: [
          {
            path: ROUTERS_PATH.checkout,
            element: <pageModules.Checkout />,
          },
        ],
      },
      {
        path: '/',
        element: <AccountLayout />,
        children: [
          {
            path: ROUTERS_PATH.clinicManagement,
            element: <pageModules.ClinicManagement />,
          },
          {
            path: ROUTERS_PATH.accountDashboard,
            element: <pageModules.Dashboard />,
          },
          {
            path: ROUTERS_PATH.accountDetails,
            element: <SettingsLayout title="Account Info" />,
            children: [
              { index: true, element: <pageModules.AccountDetails /> },
            ],
          },
        ],
      },
      {
        path: '*',
        element: <pageModules.Page404 />,
      },
    ],
  },
];

const router = createBrowserRouter(routes);

export default router;
