import { enableMapSet } from 'immer';
import { apiErrorNotification, createStore } from '@/utils';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { CartType } from '@/libs/cart/types';
import { deleteApi, get, post } from '@/libs/utils/api';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { ApiErrorProps } from '@/types/utility';
import type { CheckoutResponseType } from './type';

enableMapSet();

type State = CartType & {
  isCartLoading: boolean;
  errorOnCartLoading: boolean;
  updatingProductIds: Set<string>;
};
type Actions = {
  fetchCart: VoidFunction;
  clearCart: VoidFunction;
  setProductUpdating: (productOfferId: string, isUpdating: boolean) => void;
  swapOfferCartItem: (itemId: string, productOfferId: string) => void;
  checkout: () => Promise<CheckoutResponseType>;
  addToCart: (params: {
    productOfferId: string;
    quantity: number;
    onError: (message: string) => void;
  }) => void;
};

export const INITIAL_STATE: State = {
  budget: null,
  vendors: [],
  isCartLoading: false,
  errorOnCartLoading: false,
  itemsCount: 0,
  uniqueItemsCount: 0,
  subtotal: '0',
  total: '0',
  updatingProductIds: new Set<string>(),
};

export const useCartStore = createStore<Actions & State>()(
  immer(
    devtools((set, getState) => ({
      ...INITIAL_STATE,
      fetchCart: async () => {
        set({ isCartLoading: true, errorOnCartLoading: false });

        const clinicId = useClinicStore.getState().clinic?.id;
        try {
          const response = await get<CartType>({
            url: `/clinics/${clinicId}/cart`,
          });

          set(response);
        } catch (error) {
          // TODO: Handle it better
          set({ errorOnCartLoading: true });
          console.error(error);
        }

        set({ isCartLoading: false });
      },
      clearCart: async () => {
        set({ isCartLoading: false });
        const clinic = useClinicStore.getState().clinic;

        const { budget } = await deleteApi<{
          budget: CartType['budget'];
        }>({
          url: `/clinics/${clinic?.id}/cart`,
        });

        set({
          ...INITIAL_STATE,
          budget: budget,
        });
      },
      setProductUpdating: (productOfferId, isUpdating) => {
        const { updatingProductIds } = getState();

        if (isUpdating) {
          updatingProductIds.add(productOfferId);
        } else {
          updatingProductIds.delete(productOfferId);
        }

        set({
          isCartLoading: isUpdating,
          updatingProductIds: updatingProductIds,
        });
      },
      addToCart: async ({ productOfferId, quantity, onError }) => {
        const state = getState();

        const currentItem = state.vendors
          .flatMap((vendor) => vendor.items)
          .find((item) => item.productOfferId === productOfferId);

        if (currentItem?.quantity === quantity) {
          return;
        }

        state.setProductUpdating(productOfferId, true);

        try {
          const clinicId = useClinicStore.getState().clinic?.id;
          const response = await post<CartType>({
            url: `/clinics/${clinicId}/cart/cart-items`,
            method: 'PATCH',
            body: {
              productOfferId,
              quantity,
              notes: '',
            },
          });

          set(response);
        } catch (err) {
          const { data } = err as ApiErrorProps;
          apiErrorNotification(data.message);
          onError(data?.message ?? '');
        }

        getState().setProductUpdating(productOfferId, false);
      },
      checkout: async () => {
        const clinic = useClinicStore.getState().clinic;

        const data = await post<CheckoutResponseType>({
          url: `/clinics/${clinic?.id}/orders`,
          body: {
            isBillingSameAsShippingAddress: true,
            paymentMethod: 'INVOICE',
            shippingAddress: clinic?.shippingAddress,
            billingAddress: clinic?.billingAddress,
          },
        });

        return data;
      },
      swapOfferCartItem: async (itemId, productOfferId) => {
        try {
          const clinicId = useClinicStore.getState().clinic?.id;
          const response = await post<CartType>({
            url: `/clinics/${clinicId}/cart/cart-items/${itemId}`,
            method: 'PATCH',
            body: {
              productOfferId,
            },
          });

          set(response);
        } catch (err) {
          const { data } = err as ApiErrorProps;

          apiErrorNotification(data.message);
        }
      },
    })),
  ),
);
