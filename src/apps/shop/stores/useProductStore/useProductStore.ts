import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { get, deleteApi, post } from '@/libs/utils/api';
import { GetDataWithPagination } from '@/types/utility';
import { buildQueryString, createStore } from '@/utils';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';

import { Actions, SearchState, State } from './types';
import { ProductType, SearchParamsProps } from '@/types';

const SEARCH_INITIAL_STATE: SearchState = {
  productList: [],
  page: 1,
  total: 0,
  perPage: '12',
  query: '',
  sortOptions: {
    sortOrder: undefined,
    sortBy: undefined,
  },
  filterOptions: {
    vendorIds: '',
  },
};

export const useProductStore = createStore<State & Actions>()(
  immer(
    devtools((set, getState) => ({
      ...SEARCH_INITIAL_STATE,
      fetchProductDetails: async (id?: string) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        const response = await get<ProductType>({
          url: `/clinics/${clinicId}/products/${id}`,
        });

        return response;
      },
      updateSearchQueryValue: (query) => set({ query }),
      getSearchProduct: async (
        params: Partial<SearchParamsProps<ProductType>>,
        updateQuery,
      ) => {
        const { sortOptions, query } = getState();
        const clinicId = useClinicStore.getState().clinic?.id;

        const queryParams: SearchParamsProps<ProductType> = {
          query: query.trim(),
          sortBy: sortOptions.sortBy,
          sortOrder: sortOptions.sortOrder,
          clinicId: clinicId || '',
          page: params.page || 1,
          perPage: params.perPage || '12',
          vendorIds: params.vendorIds || '',
          ...params,
        };

        if (!queryParams.query) {
          return;
        }

        const { sortBy, sortOrder, page, perPage, vendorIds, ...rest } =
          queryParams;
        let newQuery =
          buildQueryString<Partial<SearchParamsProps<ProductType>>>(rest);

        newQuery += page ? `&page[number]=${page}` : '';
        newQuery += perPage ? `&page[size]=${perPage}` : '';
        newQuery += vendorIds ? `&filter[vendorIds]=${vendorIds}` : '';

        const response = await get<GetDataWithPagination<ProductType>>({
          url: `/clinics/${clinicId}/search?${newQuery}`,
        });

        // TBD the implementation of sorting by vendor
        // Bubble up first vendor filter to the top of the offers list
        // if (vendorIds?.length) {
        //   response.data.forEach((item) => {
        //     item.offers.sort((offer) =>
        //       offer.vendor.id === vendorIds![0] ? -1 : 1,
        //     );
        //   });
        // }

        set({
          productList: response.data,
          total: response.meta.total,
          sortOptions: {
            sortBy,
            sortOrder,
          },
          filterOptions: {
            vendorIds: vendorIds ?? '',
          },
          page,
          perPage,
          ...rest,
        });

        updateQuery(
          buildQueryString<SearchParamsProps<ProductType>>(queryParams),
        );
      },
      clearSearchProduct: () => set({ ...SEARCH_INITIAL_STATE }),
      addToFavorite: async (productId) => {
        const clinicId = useClinicStore.getState().clinic?.id;
        try {
          await post<ProductType>({
            url: `/clinics/${clinicId}/favorite-products`,
            body: {
              productId,
            },
          });

          return true;
        } catch {
          return false;
        }
      },
      removeToFavorite: async (productId) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        try {
          await deleteApi({
            url: `/clinics/${clinicId}/favorite-products/${productId}`,
          });

          return true;
        } catch {
          return false;
        }
      },
      updateProductList: (list) => {
        set({ productList: list });
      },
    })),
  ),
);
