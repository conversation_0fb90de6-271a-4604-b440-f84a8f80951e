import { useState, useEffect, useCallback } from 'react';
import { get } from '@/libs/utils/api';
import { useClinicStore } from '../../stores/useClinicStore';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';

export const useProductSuggestions = (query: string) => {
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [lastEmptyQuery, setLastEmptyQuery] = useState<string>('');
  const { clinic } = useClinicStore();
  const debouncedQuery = useDebounce(query, 300);

  const apiFunc = useCallback(
    async (searchQuery?: string) => {
      if (!searchQuery?.trim() || !clinic?.id) {
        setSearchSuggestions([]);
        setLastEmptyQuery('');
        return;
      }

      const trimmedQuery = searchQuery.trim();

      if (lastEmptyQuery && trimmedQuery.startsWith(lastEmptyQuery)) {
        setSearchSuggestions([]);
        return;
      }

      try {
        const response = await get<{ data: string[] }>({
          url: `/clinics/${clinic.id}/autocomplete?query=${encodeURIComponent(trimmedQuery)}&clinicId=${clinic.id}`,
        });

        const results = response?.data || [];
        setSearchSuggestions(results);

        if (results.length === 0) {
          setLastEmptyQuery(trimmedQuery);
        } else {
          setLastEmptyQuery('');
        }
      } catch {
        setSearchSuggestions([]);
        setLastEmptyQuery(trimmedQuery);
      }
    },
    [clinic?.id, lastEmptyQuery],
  );

  const { apiRequest, isLoading: isSuggestionsLoading } = useAsyncRequest({
    apiFunc,
  });

  useEffect(() => {
    if (debouncedQuery && debouncedQuery.length > 2) {
      // It means its a new query
      if (lastEmptyQuery && debouncedQuery.length < lastEmptyQuery.length) {
        setLastEmptyQuery('');
      }
      apiRequest(debouncedQuery);
    } else {
      setSearchSuggestions([]);
      setLastEmptyQuery('');
    }
  }, [debouncedQuery, apiRequest, lastEmptyQuery]);

  return {
    searchSuggestions,
    isSuggestionsLoading,
    debouncedQuery,
  };
};
