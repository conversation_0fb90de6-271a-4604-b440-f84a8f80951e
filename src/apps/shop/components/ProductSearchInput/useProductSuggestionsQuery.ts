import { useState, useEffect } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useClinicStore } from '../../stores/useClinicStore';
import { useDebounce } from '@/libs/utils/hooks/useDebounce/useDebounce';
import { queryKeys, cacheConfig } from '@/libs/query/queryClient';
import { get } from '@/libs/utils/api';

/**
 * Enhanced useProductSuggestions using TanStack Query
 *
 * Features:
 * - Automatic caching with smart invalidation
 * - Background refetching
 * - Optimistic "no results" caching
 * - Automatic retry with exponential backoff
 * - Built-in loading and error states
 * - DevTools integration for debugging
 */
export const useProductSuggestionsQuery = (query: string) => {
  const [lastEmptyQuery, setLastEmptyQuery] = useState<string>('');
  const { clinic } = useClinicStore();
  const debouncedQuery = useDebounce(query, 300);
  const queryClient = useQueryClient();

  // Generate query key for this specific search
  const queryKey = queryKeys.products.suggestions(
    clinic?.id || '',
    debouncedQuery.trim().toLowerCase(),
  );

  // Check if we should skip the query based on previous empty results
  const shouldSkipQuery = () => {
    if (!debouncedQuery?.trim() || !clinic?.id || debouncedQuery.length <= 2) {
      return true;
    }

    const trimmedQuery = debouncedQuery.trim().toLowerCase();

    // Check if this query starts with a previous empty query
    if (lastEmptyQuery && trimmedQuery.startsWith(lastEmptyQuery)) {
      return true;
    }

    // Check cache for shorter queries that returned empty results
    for (let i = 1; i < trimmedQuery.length; i++) {
      const shorterQuery = trimmedQuery.substring(0, i);
      const shorterQueryKey = queryKeys.products.suggestions(
        clinic.id,
        shorterQuery,
      );
      const cachedData = queryClient.getQueryData(shorterQueryKey);

      if (cachedData && Array.isArray(cachedData) && cachedData.length === 0) {
        setLastEmptyQuery(shorterQuery);
        return true;
      }
    }

    return false;
  };

  // TanStack Query for fetching suggestions
  const {
    data: searchSuggestions = [],
    isLoading: isSuggestionsLoading,
    error,
    refetch,
    isFetching,
    isStale,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      const trimmedQuery = debouncedQuery.trim();

      try {
        const response = await get<{ data: string[] }>({
          url: `/clinics/${clinic?.id}/autocomplete?query=${encodeURIComponent(trimmedQuery)}&clinicId=${clinic?.id}`,
        });

        const results = response?.data || [];

        // Track empty results for optimization
        if (results.length === 0) {
          setLastEmptyQuery(trimmedQuery.toLowerCase());
        } else {
          // Clear empty query tracking if we got results
          setLastEmptyQuery('');
        }

        return results;
      } catch (error) {
        // On error, treat as empty result and track it
        setLastEmptyQuery(trimmedQuery.toLowerCase());
        throw error;
      }
    },
    enabled: !shouldSkipQuery(),
    ...cacheConfig.search,
    // Additional TanStack Query options
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: (failureCount, error: any) => {
      // Don't retry on 404 (no results) or 400 (bad request)
      if (error?.status === 404 || error?.status === 400) {
        return false;
      }
      return failureCount < 2;
    },
  });

  // Reset empty query tracking when query gets shorter
  useEffect(() => {
    if (lastEmptyQuery && debouncedQuery.length < lastEmptyQuery.length) {
      setLastEmptyQuery('');
    }
  }, [debouncedQuery, lastEmptyQuery]);

  // Clear suggestions and tracking when query is too short
  useEffect(() => {
    if (!debouncedQuery || debouncedQuery.length <= 2) {
      setLastEmptyQuery('');
    }
  }, [debouncedQuery]);

  // Utility functions for cache management
  const clearSuggestionsCache = () => {
    if (clinic?.id) {
      // Clear all suggestion caches for this clinic
      queryClient.removeQueries({
        predicate: (query) => {
          const key = query.queryKey;
          return (
            key[0] === 'products' &&
            key[1] === 'suggestions' &&
            key[2] === clinic.id
          );
        },
      });
      setLastEmptyQuery('');
    }
  };

  const preloadSuggestions = async (queries: string[]) => {
    if (!clinic?.id) return;

    // Preload suggestions for common queries
    const promises = queries.map(async (q) => {
      if (q.length > 2) {
        const key = queryKeys.products.suggestions(clinic.id, q.toLowerCase());
        await queryClient.prefetchQuery({
          queryKey: key,
          queryFn: () =>
            get<{ data: string[] }>({
              url: `/clinics/${clinic.id}/autocomplete?query=${encodeURIComponent(q)}&clinicId=${clinic.id}`,
            }),
          ...cacheConfig.search,
        });
      }
    });

    await Promise.allSettled(promises);
  };

  // Get cache statistics for debugging
  const getCacheStats = () => {
    const cache = queryClient.getQueryCache();
    const suggestionQueries = cache
      .getAll()
      .filter(
        (query) =>
          query.queryKey[0] === 'products' &&
          query.queryKey[1] === 'suggestions',
      );

    return {
      totalSuggestionQueries: suggestionQueries.length,
      staleSuggestionQueries: suggestionQueries.filter((q) => q.isStale())
        .length,
      fetchingSuggestionQueries: suggestionQueries.filter((q) => q.isFetching())
        .length,
    };
  };

  return {
    // Main data and states
    searchSuggestions,
    isSuggestionsLoading,
    debouncedQuery,

    // Additional TanStack Query states
    error,
    isFetching, // True when actively fetching (including background refetch)
    isStale, // True when data is stale but still being shown

    // Cache management functions
    refetchSuggestions: refetch,
    clearSuggestionsCache,
    preloadSuggestions,
    getCacheStats,

    // Query client access for advanced usage
    queryClient,
    queryKey,
  };
};

/**
 * Drop-in replacement for the original useProductSuggestions
 * This provides the same interface while using TanStack Query underneath
 */
export const useProductSuggestions = useProductSuggestionsQuery;
