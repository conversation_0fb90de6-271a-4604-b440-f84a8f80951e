import { ChangeEvent, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';

import styles from './ProductSearchInput.module.css';

import { buildQueryString } from '@/utils/buildQueryString';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';

import { useProductSuggestions } from './useProductSuggestions';
import { Combobox } from '@/libs/ui/Combobox/Combobox';
import { UnstyledButton } from '@mantine/core';

export const ProductSearchInput = () => {
  const { t } = useTranslation();
  const [query, setQuery] = useState<string>('');
  const [selectedProduct, setSelectedProduct] = useState<string>('');

  const navigate = useNavigate();
  const { pathname } = useLocation();
  const [searchParams] = useSearchParams();

  // Get current query from URL if on search page
  const currentQuery =
    pathname === ROUTERS_PATH.search ? searchParams.get('query') || '' : query;

  const { searchSuggestions, isSuggestionsLoading } =
    useProductSuggestions(currentQuery);

  const handleSearchQuery = async (value: string) => {
    const params = {
      query: value.trim(),
      page: 1,
      perPage: '12',
    };
    const newQuery = buildQueryString(params);

    navigate(`${ROUTERS_PATH.search}?${newQuery}`);
  };

  const handleProductSelect = (value: unknown) => {
    const productName = value as string;
    if (productName) {
      setSelectedProduct(productName);
      setQuery(productName);
      handleSearchQuery(productName);
    }
  };

  const handleInputChange = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setQuery(value);
    setSelectedProduct('');
  };

  const handleEnterPress = (searchTerm: string) => {
    handleSearchQuery(searchTerm);
  };

  return (
    <div className={styles.container}>
      <Combobox
        value={selectedProduct || currentQuery}
        onChange={handleProductSelect}
        isLoading={isSuggestionsLoading}
      >
        <Combobox.Input
          placeholder={t('common.searchProducts')}
          onChange={handleInputChange}
          onEnterPress={handleEnterPress}
          className={styles.searchInput}
          displayValue={(value) => (value as string) || currentQuery}
        />

        <Combobox.Options>
          {searchSuggestions.map((suggestion) => (
            <Combobox.Option key={suggestion} value={suggestion}>
              <UnstyledButton className={styles.productName}>
                {suggestion}
              </UnstyledButton>
            </Combobox.Option>
          ))}
        </Combobox.Options>
      </Combobox>
    </div>
  );
};
