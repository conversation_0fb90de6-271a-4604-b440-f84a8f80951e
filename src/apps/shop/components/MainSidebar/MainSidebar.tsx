import { Box, Button, Flex, Text } from '@mantine/core';

import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import LockIcon from './assets/lock.svg?react';

import { useTranslation } from 'react-i18next';
import type { AccountType, ClinicType } from '@/types/common.ts';
import {
  SidebarNav,
  type SidebarNavProps,
} from '@/libs/ui/SidebarNav/SidebarNav';
import { UserSection } from '../UserSection/UserSection';
import styles from './MainSidebar.module.css';
import QuestionMarkIcon from './assets/question.svg?react';
import { HF_CONTACT_EMAIL } from '@/constants';
import { Logo } from '@/libs/ui/Logo/Logo';
import { Link } from 'react-router-dom';

function getAccess({
  clinic,
  account,
}: {
  clinic?: ClinicType | null;
  account: AccountType | null;
}): boolean {
  if (clinic) {
    return clinic.hasAnyVendorConnected;
  }

  return account?.hasAnyVendorConnected as boolean;
}
interface MainSidebarProps {
  navLinkGroups: SidebarNavProps[];
}
export const MainSidebar = ({ navLinkGroups }: MainSidebarProps) => {
  const { t } = useTranslation();
  const { getGpo } = useAuthStore();
  const { clinic } = useClinicStore();
  const { account } = useAccountStore();

  const gpo = getGpo();
  const sideBarAccess = getAccess({ clinic, account });

  return (
    <div className={styles.sidebarRoot}>
      {sideBarAccess ? (
        <>
          <Box maw="80%" mah="8rem" mx="auto" mt="3.5rem" mb="1.5rem">
            <Link to="/">
              {gpo?.imageUrl ? (
                <img width="100%" src={gpo?.imageUrl} alt="logo" />
              ) : (
                <Logo />
              )}
            </Link>
          </Box>
          <Flex direction="column" h="100%" justify="space-between">
            <Box>
              {navLinkGroups.map(({ title, links }) => (
                <SidebarNav key={title} title={title} links={links} />
              ))}

              <Box mt="md">
                <Button
                  component="a"
                  href={`mailto:${HF_CONTACT_EMAIL}`}
                  variant="default"
                  leftSection={<QuestionMarkIcon />}
                  fullWidth
                >
                  Need Help
                </Button>
              </Box>
            </Box>
            <Box mb="md">
              <UserSection />
              {gpo ? (
                <Flex align="center" gap="0.25rem" mt="md">
                  <Text mih="1rem">Powered by</Text>
                  <Box w="100px">
                    <Logo type="full" />
                  </Box>
                </Flex>
              ) : null}
            </Box>
          </Flex>
        </>
      ) : (
        <div className={styles.lock}>
          <LockIcon />
          <Text fw="500">{t('sidebar.blockMessage')}</Text>
        </div>
      )}
    </div>
  );
};
