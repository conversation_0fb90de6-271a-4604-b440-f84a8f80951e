import { Suspense, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { LoadingOverlay } from '@mantine/core';

import { resetAllStores } from '@/utils';

import styles from './AuthLayout.module.css';
import { Logo } from '@/libs/ui/Logo/Logo';

const LOADER_PROPS = {
  mt: '250px',
  size: '3rem',
};

export const AuthLayout = () => {
  useEffect(() => {
    resetAllStores();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.wrapper}>
      <div className={styles.contentBox}>
        <Logo />

        <Suspense
          fallback={<LoadingOverlay loaderProps={LOADER_PROPS} visible />}
        >
          <Outlet />
        </Suspense>
      </div>
    </div>
  );
};
