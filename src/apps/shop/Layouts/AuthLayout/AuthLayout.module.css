.wrapper {
  display: flex;
  align-items: center;
  height: 100vh;
  background-image: linear-gradient(
      90deg,
      rgba(233, 205, 104, 0.15) 0%,
      rgba(189, 220, 240, 0.15) 50%,
      rgba(234, 197, 215, 0.15) 100%
    ),
    linear-gradient(
      90deg,
      var(--mantine-color-white) 50%,
      var(--mantine-color-light-blue-3) 100%
    );
}

.contentBox {
  max-width: 360px;
  width: 100%;
  margin: 0 auto;

  > img {
    max-width: 245px;
    margin: 0 auto 2rem;
  }
}

body {
  .title {
    color: var(--mantine-color-dark-8);
    font-weight: 700;
    margin-bottom: 0.5rem;
  }

  .subtitle {
    color: var(--mantine-color-dark-6);
    margin-bottom: 2rem;
  }
}

.form {
  & > *:not(:last-child) {
    margin-bottom: 1.5rem;
  }

  div.info {
    display: flex;
    justify-content: space-between;

    a {
      color: var(--mantine-color-dark-8);
    }
  }
}

.footer {
  margin-top: 1.5rem;

  a {
    margin-left: 0.25rem;
    font-weight: bold;
    color: var(--mantine-color-dark-8);
  }
}
