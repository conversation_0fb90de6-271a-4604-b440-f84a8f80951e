import { Flex, Loader } from '@mantine/core';
import { Suspense, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { ErrorBoundary } from 'react-error-boundary';

import { ErrorSection } from '@/components';
import { ProtectedLayout } from '@/apps/shop/Layouts/ProtectedLayout/ProtectedLayout';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';

import { TopNavbar } from '@/libs/ui/TopNavbar/TopNavbar';
import { MainSidebar } from '@/apps/shop/components/MainSidebar/MainSidebar';
import {
  ACCOUNT_NAV_LINKS,
  ACCOUNT_SETTING_LINKS,
} from '@/apps/shop/constants';
import { AppContentWrap } from '@/libs/ui/AppContentWrap/AppContentWrap';

export const AccountLayout = () => {
  const { clearClinic } = useClinicStore();
  const { account } = useAccountStore();

  useEffect(() => {
    clearClinic();
  }, [clearClinic]);

  const navLinkGroups = [
    {
      title: 'Main menu',
      links: ACCOUNT_NAV_LINKS,
    },
    {
      title: 'Preferences',
      links: ACCOUNT_SETTING_LINKS,
    },
  ];

  return (
    <ErrorBoundary fallback={<ErrorSection />}>
      <Suspense
        fallback={
          <div className="loaderRoot height100vh">
            <Loader size="3rem" />
          </div>
        }
      >
        <ProtectedLayout>
          <AppContentWrap>
            <Flex>
              <MainSidebar navLinkGroups={navLinkGroups} />
              <Flex direction="column" w="100%">
                <TopNavbar title={account?.name} />
                <ErrorBoundary fallback={<ErrorSection />}>
                  <Outlet />
                </ErrorBoundary>
              </Flex>
            </Flex>
          </AppContentWrap>
        </ProtectedLayout>
      </Suspense>
    </ErrorBoundary>
  );
};
