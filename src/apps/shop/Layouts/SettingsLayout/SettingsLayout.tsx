import { Box, Flex, Text } from '@mantine/core';
import styles from './SettingsLayout.module.css';
import { Outlet } from 'react-router-dom';

export const SettingsLayout = ({ title }: { title: string }) => {
  return (
    <Box p="2rem">
      <Box bg="#fff" p="1.5rem" className={styles.container}>
        <Flex className={styles.innerContainer} justify="center" py="md">
          <Flex
            direction="column"
            bg="#fff"
            className={styles.container}
            p="1.5rem"
            w="690px"
          >
            <Text size="1.2rem" fw="500">
              {title}
            </Text>
            <Outlet />
          </Flex>
        </Flex>
      </Box>
    </Box>
  );
};
