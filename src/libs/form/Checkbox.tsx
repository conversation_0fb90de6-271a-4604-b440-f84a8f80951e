import { forwardRef, InputHTMLAttributes } from 'react';
import styles from './Input.module.css';
import { Flex } from '@/libs/ui/Flex/Flex';

type InputProps = {
  id?: string;
  label?: string;
  error?: string;
  variant?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
} & InputHTMLAttributes<HTMLInputElement>;

export const Checkbox = forwardRef<HTMLInputElement, InputProps>(
  ({ id, label, error, align = 'left', onChange, ...rest }, ref) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e);
      }
    };

    return (
      <>
        <Flex pos="relative" component="label">
          <input
            id={id}
            ref={ref}
            onChange={handleChange}
            aria-invalid={!!error}
            className={styles.checkbox}
            style={{ textAlign: align }}
            type="checkbox"
            {...rest}
          />
          {label && (
            <label htmlFor={id} className={styles.label}>
              {label}
            </label>
          )}
        </Flex>
        {error && <p className={styles.error}>{error}</p>}
      </>
    );
  },
);

Checkbox.displayName = 'Checkbox';
