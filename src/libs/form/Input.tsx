import {
  forwardRef,
  InputHTMLAttributes,
  ReactElement,
  ReactNode,
  useState,
} from 'react';
import styles from './Input.module.css';
import clsx from 'clsx';
import EyeIcon from './assets/eye.svg?react';
import EyeCrossIcon from './assets/eye-cross.svg?react';
import { Box, Flex, UnstyledButton } from '@mantine/core';
import { HelpTooltip } from '../ui/HelpTooltip/HelpTooltip';

export type InputProps = {
  id?: string;
  label?: string | ReactNode;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
  mask?: (value: string) => string;
  tooltip?: string;
  rightSection?: ReactElement;
} & Omit<InputHTMLAttributes<HTMLInputElement>, 'size'>;

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      id,
      label,
      error,
      mask,
      size = 'md',
      align = 'left',
      type: originalType,
      onChange,
      tooltip,
      rightSection,
      ...rest
    },
    ref,
  ) => {
    const [type, setType] = useState(originalType);
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (mask) {
        e.target.value = mask(e.target.value);
      }

      if (onChange) {
        onChange(e);
      }
    };

    const handleSwitchType = () => {
      setType((type) => (type === 'password' ? 'text' : 'password'));
    };

    return (
      <div className={styles.container}>
        {label || tooltip ? (
          <Flex gap="0.5rem" align="center" mb="0.4rem">
            {label && (
              <label htmlFor={id} className={styles.label}>
                {label}
              </label>
            )}
            {tooltip && <HelpTooltip message={tooltip} />}
          </Flex>
        ) : null}
        <Box pos="relative">
          <input
            id={id}
            ref={(inputRef) => {
              if (typeof ref === 'function') {
                ref(inputRef);
              } else if (ref && 'current' in ref) {
                ref.current = inputRef;
              }

              if (mask && inputRef) {
                inputRef.value = mask(inputRef.value);
              }
            }}
            onChange={handleChange}
            aria-invalid={!!error}
            style={{ textAlign: align }}
            type={type}
            {...rest}
            className={clsx(styles.input, {
              [styles.small]: size === 'sm',
              [styles.large]: size === 'lg',
            })}
          />
          {rightSection ? (
            <div className={styles.rightSection}>{rightSection}</div>
          ) : originalType === 'password' ? (
            <UnstyledButton
              onClick={handleSwitchType}
              className={styles.rightSection}
            >
              {type === 'password' ? <EyeIcon /> : <EyeCrossIcon />}
            </UnstyledButton>
          ) : null}
        </Box>
        {error && <p className={styles.error}>{error}</p>}
      </div>
    );
  },
);

Input.displayName = 'Input';
