import { get, post, deleteApi } from '@/libs/utils/api';
import { queryClient, queryKeys } from './queryClient';

// Query functions that integrate with your existing API utilities

// User queries
export const userQueries = {
  // Get current user
  me: () => ({
    queryKey: queryKeys.user.me(),
    queryFn: () => get<any>({ url: '/users/me' }),
  }),

  // Get user profile
  profile: (userId: string) => ({
    queryKey: queryKeys.user.profile(userId),
    queryFn: () => get<any>({ url: `/users/${userId}` }),
  }),
};

// Clinic queries
export const clinicQueries = {
  // Get clinic details
  details: (clinicId: string) => ({
    queryKey: queryKeys.clinic.details(clinicId),
    queryFn: () => get<any>({ url: `/clinics/${clinicId}` }),
  }),

  // Get clinic info
  info: (clinicId: string) => ({
    queryKey: queryKeys.clinic.info(clinicId),
    queryFn: () => get<any>({ url: `/clinics/${clinicId}` }),
  }),
};

// Product queries
export const productQueries = {
  // Get product list with filters
  list: (clinicId: string, filters: Record<string, any> = {}) => ({
    queryKey: queryKeys.products.list(filters),
    queryFn: () => {
      const queryString = new URLSearchParams(filters).toString();
      return get<any>({
        url: `/clinics/${clinicId}/products${queryString ? `?${queryString}` : ''}`,
      });
    },
  }),

  // Get product details
  details: (clinicId: string, productId: string) => ({
    queryKey: queryKeys.products.details(clinicId, productId),
    queryFn: () =>
      get<any>({ url: `/clinics/${clinicId}/products/${productId}` }),
  }),

  // Search products
  search: (clinicId: string, query: string) => ({
    queryKey: queryKeys.products.search(clinicId, query),
    queryFn: () =>
      get<any>({
        url: `/clinics/${clinicId}/products/search?query=${encodeURIComponent(query)}`,
      }),
    enabled: query.length > 2, // Only search if query is longer than 2 characters
  }),

  // Get product suggestions/autocomplete
  suggestions: (clinicId: string, query: string) => ({
    queryKey: queryKeys.products.suggestions(clinicId, query),
    queryFn: () =>
      get<{ data: string[] }>({
        url: `/clinics/${clinicId}/autocomplete?query=${encodeURIComponent(query)}&clinicId=${clinicId}`,
      }),
    enabled: query.length > 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  }),
};

// Cart queries
export const cartQueries = {
  // Get cart details
  details: (clinicId: string) => ({
    queryKey: queryKeys.cart.details(clinicId),
    queryFn: () => get<any>({ url: `/clinics/${clinicId}/cart` }),
  }),
};

// Order queries
export const orderQueries = {
  // Get order list with filters
  list: (filters: Record<string, any> = {}) => ({
    queryKey: queryKeys.orders.list(filters),
    queryFn: () => {
      const queryString = new URLSearchParams(filters).toString();
      return get<any>({
        url: `/orders${queryString ? `?${queryString}` : ''}`,
      });
    },
  }),

  // Get order details
  details: (orderId: string) => ({
    queryKey: queryKeys.orders.details(orderId),
    queryFn: () => get<any>({ url: `/orders/${orderId}` }),
  }),
};

// Account queries
export const accountQueries = {
  // Get account details
  details: (accountId: string) => ({
    queryKey: queryKeys.account.details(accountId),
    queryFn: () => get<any>({ url: `/accounts/${accountId}` }),
  }),
};

// Mutation functions with automatic cache invalidation
export const mutations = {
  // Cart mutations
  cart: {
    addItem: (clinicId: string) => ({
      mutationFn: (item: any) =>
        post({
          url: `/clinics/${clinicId}/cart/items`,
          body: item,
        }),
      onSuccess: () => {
        // Invalidate cart queries
        queryClient.invalidateQueries({
          queryKey: queryKeys.cart.details(clinicId),
        });
      },
    }),

    updateItem: (clinicId: string) => ({
      mutationFn: ({ itemId, ...data }: { itemId: string } & any) =>
        post({
          url: `/clinics/${clinicId}/cart/items/${itemId}`,
          body: data,
          method: 'PUT',
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.cart.details(clinicId),
        });
      },
    }),

    removeItem: (clinicId: string) => ({
      mutationFn: (itemId: string) =>
        deleteApi({
          url: `/clinics/${clinicId}/cart/items/${itemId}`,
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.cart.details(clinicId),
        });
      },
    }),

    clear: (clinicId: string) => ({
      mutationFn: () => deleteApi({ url: `/clinics/${clinicId}/cart` }),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.cart.details(clinicId),
        });
      },
    }),
  },

  // Product mutations
  products: {
    update: (clinicId: string, productId: string) => ({
      mutationFn: (data: any) =>
        post({
          url: `/clinics/${clinicId}/products/${productId}`,
          body: data,
          method: 'PUT',
        }),
      onSuccess: () => {
        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: queryKeys.products.details(clinicId, productId),
        });
        queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() });
        queryClient.invalidateQueries({ queryKey: queryKeys.products.all });
      },
    }),
  },

  // Order mutations
  orders: {
    create: () => ({
      mutationFn: (orderData: any) =>
        post({
          url: '/orders',
          body: orderData,
        }),
      onSuccess: () => {
        // Invalidate order lists
        queryClient.invalidateQueries({ queryKey: queryKeys.orders.lists() });
      },
    }),
  },

  // User mutations
  user: {
    update: (userId: string) => ({
      mutationFn: (data: any) =>
        post({
          url: `/users/${userId}`,
          body: data,
          method: 'PUT',
        }),
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.user.profile(userId),
        });
        queryClient.invalidateQueries({ queryKey: queryKeys.user.me() });
      },
    }),
  },
};
