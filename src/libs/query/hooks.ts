import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  userQueries,
  clinicQueries,
  productQueries,
  cartQueries,
  orderQueries,
  accountQueries,
  mutations,
} from './api';
import { cacheConfig } from './queryClient';

// User hooks
export const useCurrentUser = () => {
  return useQuery({
    ...userQueries.me(),
    ...cacheConfig.user,
  });
};

export const useUserProfile = (userId: string) => {
  return useQuery({
    ...userQueries.profile(userId),
    ...cacheConfig.user,
    enabled: !!userId,
  });
};

// Clinic hooks
export const useClinicDetails = (clinicId: string) => {
  return useQuery({
    ...clinicQueries.details(clinicId),
    ...cacheConfig.user,
    enabled: !!clinicId,
  });
};

export const useClinicInfo = (clinicId: string) => {
  return useQuery({
    ...clinicQueries.info(clinicId),
    ...cacheConfig.user,
    enabled: !!clinicId,
  });
};

// Product hooks
export const useProducts = (
  clinicId: string,
  filters: Record<string, any> = {},
) => {
  return useQuery({
    ...productQueries.list(clinicId, filters),
    ...cacheConfig.products,
    enabled: !!clinicId,
  });
};

export const useProductDetails = (clinicId: string, productId: string) => {
  return useQuery({
    ...productQueries.details(clinicId, productId),
    ...cacheConfig.products,
    enabled: !!clinicId && !!productId,
  });
};

export const useProductSearch = (clinicId: string, query: string) => {
  return useQuery({
    ...productQueries.search(clinicId, query),
    ...cacheConfig.search,
    enabled: !!clinicId && query.length > 2,
  });
};

export const useProductSuggestions = (clinicId: string, query: string) => {
  return useQuery({
    ...productQueries.suggestions(clinicId, query),
    ...cacheConfig.search,
    enabled: !!clinicId && query.length > 2,
  });
};

// Cart hooks
export const useCart = (clinicId: string) => {
  return useQuery({
    ...cartQueries.details(clinicId),
    ...cacheConfig.dynamic,
    enabled: !!clinicId,
  });
};

// Cart mutation hooks
export const useAddToCart = (clinicId: string) => {
  return useMutation(mutations.cart.addItem(clinicId));
};

export const useUpdateCartItem = (clinicId: string) => {
  return useMutation(mutations.cart.updateItem(clinicId));
};

export const useRemoveFromCart = (clinicId: string) => {
  return useMutation(mutations.cart.removeItem(clinicId));
};

export const useClearCart = (clinicId: string) => {
  return useMutation(mutations.cart.clear(clinicId));
};

// Order hooks
export const useOrders = (filters: Record<string, any> = {}) => {
  return useQuery({
    ...orderQueries.list(filters),
    ...cacheConfig.dynamic,
  });
};

export const useOrderDetails = (orderId: string) => {
  return useQuery({
    ...orderQueries.details(orderId),
    ...cacheConfig.user,
    enabled: !!orderId,
  });
};

// Order mutation hooks
export const useCreateOrder = () => {
  return useMutation(mutations.orders.create());
};

// Account hooks
export const useAccountDetails = (accountId: string) => {
  return useQuery({
    ...accountQueries.details(accountId),
    ...cacheConfig.user,
    enabled: !!accountId,
  });
};

// User mutation hooks
export const useUpdateUser = (userId: string) => {
  return useMutation(mutations.user.update(userId));
};

// Product mutation hooks
export const useUpdateProduct = (clinicId: string, productId: string) => {
  return useMutation(mutations.products.update(clinicId, productId));
};

// Utility hooks for cache management
export const useCacheUtils = () => {
  const queryClient = useQueryClient();

  return {
    // Invalidate all queries for a specific clinic
    invalidateClinicData: (clinicId: string) => {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const key = query.queryKey;
          return key.includes('clinic') && key.includes(clinicId);
        },
      });
    },

    // Invalidate all product-related queries
    invalidateProducts: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },

    // Invalidate all cart data
    invalidateCart: (clinicId: string) => {
      queryClient.invalidateQueries({
        queryKey: ['cart', 'details', clinicId],
      });
    },

    // Prefetch critical data
    prefetchUserData: async (userId: string) => {
      await queryClient.prefetchQuery(userQueries.profile(userId));
    },

    prefetchClinicData: async (clinicId: string) => {
      await queryClient.prefetchQuery(clinicQueries.details(clinicId));
    },

    // Clear all cache
    clearAllCache: () => {
      queryClient.clear();
    },

    // Get cache stats (for debugging)
    getCacheStats: () => {
      const cache = queryClient.getQueryCache();
      const queries = cache.getAll();

      return {
        totalQueries: queries.length,
        staleQueries: queries.filter((q) => q.isStale()).length,
        fetchingQueries: queries.filter((q) => q.isFetching()).length,
        errorQueries: queries.filter((q) => q.state.status === 'error').length,
      };
    },
  };
};
