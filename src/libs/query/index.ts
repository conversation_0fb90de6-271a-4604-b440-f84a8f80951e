/**
 * TanStack Query Integration
 *
 * This module provides a complete TanStack Query setup for your application
 * with pre-configured query client, hooks, and utilities.
 */

// Core exports
export { queryClient, queryKeys, cacheConfig } from './queryClient';
export { QueryProvider } from '@/providers/QueryProvider';

// Query and mutation functions
export * from './api';

// Custom hooks
export * from './hooks';

// Re-export TanStack Query hooks for direct use
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  useSuspenseQuery,
  useQueries,
  useIsFetching,
  useIsMutating,
} from '@tanstack/react-query';
