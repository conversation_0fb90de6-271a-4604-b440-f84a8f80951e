import { QueryClient } from '@tanstack/react-query';
import { apiErrorNotification } from '@/utils';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';

// Default query options that apply to all queries
const defaultQueryOptions = {
  queries: {
    // Time before data is considered stale (5 minutes)
    staleTime: 5 * 60 * 1000,

    // Time before inactive queries are garbage collected (10 minutes)
    gcTime: 10 * 60 * 1000,

    // Retry failed requests 3 times with exponential backoff
    retry: (failureCount: number, error: any) => {
      // Don't retry on 401 (unauthorized) or 403 (forbidden)
      if (error?.status === 401 || error?.status === 403) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },

    // Retry delay with exponential backoff
    retryDelay: (attemptIndex: number) =>
      Math.min(1000 * 2 ** attemptIndex, 30000),

    // Refetch on window focus (can be overridden per query)
    refetchOnWindowFocus: false,

    // Refetch on reconnect
    refetchOnReconnect: true,

    // Refetch on mount if data is stale
    refetchOnMount: true,
  },
  mutations: {
    // Retry failed mutations once
    retry: 1,

    // Global error handler for mutations
    onError: (error: any) => {
      // Handle 401 errors globally
      if (error?.status === 401) {
        window.location.replace(ROUTERS_PATH.login);
        return;
      }

      // Show error notification for other errors
      const message = error?.data?.message || 'An error occurred';
      apiErrorNotification(message);
    },
  },
};

// Create the query client with default options
export const queryClient = new QueryClient({
  defaultOptions: defaultQueryOptions,
});

// Query key factories for consistent key generation
export const queryKeys = {
  // User-related queries
  user: {
    all: ['user'] as const,
    me: () => [...queryKeys.user.all, 'me'] as const,
    profile: (userId: string) =>
      [...queryKeys.user.all, 'profile', userId] as const,
  },

  // Clinic-related queries
  clinic: {
    all: ['clinic'] as const,
    details: (clinicId: string) =>
      [...queryKeys.clinic.all, 'details', clinicId] as const,
    info: (clinicId: string) =>
      [...queryKeys.clinic.all, 'info', clinicId] as const,
  },

  // Product-related queries
  products: {
    all: ['products'] as const,
    lists: () => [...queryKeys.products.all, 'list'] as const,
    list: (filters: Record<string, any>) =>
      [...queryKeys.products.lists(), filters] as const,
    details: (clinicId: string, productId: string) =>
      [...queryKeys.products.all, 'details', clinicId, productId] as const,
    search: (clinicId: string, query: string) =>
      [...queryKeys.products.all, 'search', clinicId, query] as const,
    suggestions: (clinicId: string, query: string) =>
      [...queryKeys.products.all, 'suggestions', clinicId, query] as const,
  },

  // Cart-related queries
  cart: {
    all: ['cart'] as const,
    details: (clinicId: string) =>
      [...queryKeys.cart.all, 'details', clinicId] as const,
  },

  // Order-related queries
  orders: {
    all: ['orders'] as const,
    lists: () => [...queryKeys.orders.all, 'list'] as const,
    list: (filters: Record<string, any>) =>
      [...queryKeys.orders.lists(), filters] as const,
    details: (orderId: string) =>
      [...queryKeys.orders.all, 'details', orderId] as const,
  },

  // Account-related queries
  account: {
    all: ['account'] as const,
    details: (accountId: string) =>
      [...queryKeys.account.all, 'details', accountId] as const,
  },
} as const;

// Cache time configurations for different types of data
export const cacheConfig = {
  // Static data that rarely changes (30 minutes)
  static: {
    staleTime: 30 * 60 * 1000,
    gcTime: 60 * 60 * 1000, // 1 hour
  },

  // User-specific data (10 minutes)
  user: {
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000, // 30 minutes
  },

  // Dynamic data that changes frequently (2 minutes)
  dynamic: {
    staleTime: 2 * 60 * 1000,
    gcTime: 10 * 60 * 1000, // 10 minutes
  },

  // Search results (5 minutes)
  search: {
    staleTime: 5 * 60 * 1000,
    gcTime: 15 * 60 * 1000, // 15 minutes
  },

  // Product data (15 minutes)
  products: {
    staleTime: 15 * 60 * 1000,
    gcTime: 45 * 60 * 1000, // 45 minutes
  },

  // Real-time data (30 seconds)
  realtime: {
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000, // 5 minutes
  },
} as const;
