import { Tooltip, UnstyledButton } from '@mantine/core';
import QuestionIcon from './assets/question.svg?react';

interface HelpTooltipProps {
  message: string;
}

export const HelpTooltip = ({ message }: HelpTooltipProps) => (
  <Tooltip
    label={message}
    position="right-start"
    offset={{ mainAxis: -10, crossAxis: 20 }}
    maw="18rem"
    multiline
  >
    <UnstyledButton>
      <QuestionIcon />
    </UnstyledButton>
  </Tooltip>
);
