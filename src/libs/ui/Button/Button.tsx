import { useEffect, useState } from 'react';
import type { ReactNode } from 'react';
import styles from './Button.module.css';
import { Link } from 'react-router-dom';
import { Box, Loader } from '@mantine/core';
import clsx from 'clsx';

export type ButtonBaseProps = {
  children: ReactNode;
  loading?: boolean;
  variant?: 'default' | 'secondary' | 'white';
  p?: string;
  size?: 'sm' | 'md';
};

type ButtonAsButton = ButtonBaseProps &
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    href?: never;
    to?: never;
    disabled?: boolean;
  };

type ButtonAsLink = ButtonBaseProps &
  React.AnchorHTMLAttributes<HTMLAnchorElement> & {
    href?: string;
    to?: string;
    disabled?: boolean;
  };

export type ButtonProps = ButtonAsButton | ButtonAsLink;

export const Button = ({
  className: extraClassName,
  ...props
}: ButtonProps) => {
  const {
    children,
    loading = false,
    disabled = false,
    variant = 'default',
    p = '0 1rem',
    size = 'md',
  } = props;

  const [showLoader, setShowLoader] = useState(loading);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (loading) {
      setShowLoader(true);
    } else {
      timeout = setTimeout(() => setShowLoader(false), 300);
    }
    return () => clearTimeout(timeout);
  }, [loading]);

  const commonClassName = clsx(styles.container, extraClassName, {
    [styles.secondary]: variant === 'secondary',
    [styles.white]: variant === 'white',
    [styles.small]: size === 'sm',
    [styles.loading]: showLoader,
  });

  const content = (
    <>
      <Box className={styles.loaderWrapper}>
        <Loader color="#FFF" size="sm" />
      </Box>
      <div className={styles.contentWrapper} style={{ padding: p }}>
        {children}
      </div>
    </>
  );

  if ('to' in props && props.to) {
    const { to, onClick, ...rest } = props;
    return (
      <Link
        to={to}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </Link>
    );
  }

  if ('href' in props && props.href) {
    const { href, onClick, ...rest } = props;
    return (
      <a
        href={href}
        onClick={onClick as React.MouseEventHandler<HTMLAnchorElement>}
        className={commonClassName}
        {...(rest as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </a>
    );
  }

  const { onClick, loading: loadingProps, ...rest } = props;

  return (
    <button
      className={commonClassName}
      onClick={onClick as React.MouseEventHandler<HTMLButtonElement>}
      disabled={loadingProps || disabled}
      {...(rest as React.ButtonHTMLAttributes<HTMLButtonElement>)}
    >
      {content}
    </button>
  );
};
