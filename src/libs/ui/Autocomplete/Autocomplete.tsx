import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useEffect,
  type ReactNode,
  type ChangeEvent,
  type KeyboardEvent,
} from 'react';
import * as Popover from '@radix-ui/react-popover';
import { cva, type VariantProps } from 'class-variance-authority';
import clsx from 'clsx';
import { Input } from '@/libs/form/Input';
import { Loader } from '@mantine/core';

// Context for managing autocomplete state
interface AutocompleteContextValue {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  selectedValue: unknown;
  setSelectedValue: (value: unknown) => void;
  inputValue: string;
  setInputValue: (value: string) => void;
  highlightedIndex: number;
  setHighlightedIndex: (index: number) => void;
  options: unknown[];
  setOptions: (options: unknown[]) => void;
  isLoading: boolean;
  getOptionValue?: (option: unknown) => string;
}

const AutocompleteContext = createContext<AutocompleteContextValue | null>(null);

const useAutocompleteContext = () => {
  const context = useContext(AutocompleteContext);
  if (!context) {
    throw new Error('Autocomplete components must be used within an Autocomplete');
  }
  return context;
};

// Main Autocomplete component
interface AutocompleteProps {
  children: ReactNode;
  value?: unknown;
  onChange?: (value: unknown) => void;
  isLoading?: boolean;
  getOptionValue?: (option: unknown) => string;
  className?: string;
}

export function Autocomplete({
  children,
  value,
  onChange,
  isLoading = false,
  getOptionValue,
  className,
}: AutocompleteProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value);
  const [inputValue, setInputValue] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [options, setOptions] = useState<unknown[]>([]);

  // Update selected value when prop changes
  useEffect(() => {
    setSelectedValue(value);
  }, [value]);

  // Handle selection
  const handleSelect = (newValue: unknown) => {
    setSelectedValue(newValue);
    setIsOpen(false);
    setHighlightedIndex(-1);
    onChange?.(newValue);
  };

  const contextValue: AutocompleteContextValue = {
    isOpen,
    setIsOpen,
    selectedValue,
    setSelectedValue: handleSelect,
    inputValue,
    setInputValue,
    highlightedIndex,
    setHighlightedIndex,
    options,
    setOptions,
    isLoading,
    getOptionValue,
  };

  return (
    <AutocompleteContext.Provider value={contextValue}>
      <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
        <div className={clsx('relative w-full', className)}>
          {children}
        </div>
      </Popover.Root>
    </AutocompleteContext.Provider>
  );
}

// Input component
interface AutocompleteInputProps {
  placeholder?: string;
  className?: string;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  onEnterPress?: (value: string) => void;
  displayValue?: (value: unknown) => string;
  label?: string | ReactNode;
  error?: string;
  size?: 'sm' | 'md' | 'lg';
  align?: 'left' | 'center' | 'right';
  mask?: (value: string) => string;
  tooltip?: string;
}

export function AutocompleteInput({
  placeholder,
  className,
  onChange,
  onEnterPress,
  displayValue,
  label,
  error,
  size,
  align,
  mask,
  tooltip,
}: AutocompleteInputProps) {
  const {
    isOpen,
    setIsOpen,
    selectedValue,
    inputValue,
    setInputValue,
    highlightedIndex,
    setHighlightedIndex,
    options,
    setSelectedValue,
    isLoading,
    getOptionValue,
  } = useAutocompleteContext();

  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);
    onChange?.(event);
    if (!isOpen) {
      setIsOpen(true);
    }
    setHighlightedIndex(-1);
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    switch (event.key) {
      case 'Enter':
        event.preventDefault();
        if (isOpen && highlightedIndex >= 0 && highlightedIndex < options.length) {
          setSelectedValue(options[highlightedIndex]);
        } else {
          const currentValue = inputValue.trim();
          if (currentValue && onEnterPress) {
            onEnterPress(currentValue);
          }
        }
        setIsOpen(false);
        break;

      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          const nextIndex = highlightedIndex < options.length - 1 ? highlightedIndex + 1 : 0;
          setHighlightedIndex(nextIndex);
        }
        break;

      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          const prevIndex = highlightedIndex > 0 ? highlightedIndex - 1 : options.length - 1;
          setHighlightedIndex(prevIndex);
        }
        break;

      case 'Escape':
        event.preventDefault();
        setIsOpen(false);
        setHighlightedIndex(-1);
        break;
    }
  };

  const handleFocus = () => {
    setIsOpen(true);
  };

  const displayedValue = (() => {
    if (isOpen && inputValue.trim()) {
      return inputValue;
    }

    if (selectedValue && displayValue) {
      return displayValue(selectedValue);
    }

    return inputValue;
  })();

  return (
    <Popover.Trigger asChild>
      <Input
        ref={inputRef}
        type="text"
        value={displayedValue}
        placeholder={placeholder}
        className={className}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        label={label}
        error={error}
        size={size}
        align={align}
        mask={mask}
        tooltip={tooltip}
        rightSection={isLoading ? <Loader size="sm" /> : undefined}
      />
    </Popover.Trigger>
  );
}

// Options container component
const autocompleteOptionsVariants = cva(
  'absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto',
  {
    variants: {
      size: {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  }
);

interface AutocompleteOptionsProps extends VariantProps<typeof autocompleteOptionsVariants> {
  children: ReactNode;
  className?: string;
}

export function AutocompleteOptions({
  children,
  className,
  size,
}: AutocompleteOptionsProps) {
  const { isOpen, setOptions } = useAutocompleteContext();

  // Extract options from children
  useEffect(() => {
    if (isOpen && children) {
      const childrenArray = React.Children.toArray(children);
      const optionValues = childrenArray
        .filter((child) => React.isValidElement(child) && child.props?.value !== undefined)
        .map((child) => React.isValidElement(child) ? child.props.value : undefined)
        .filter((value) => value !== undefined);

      setOptions(optionValues);
    }
  }, [children, isOpen, setOptions]);

  if (!isOpen) return null;

  const hasNoChildren = !children || (Array.isArray(children) && children.length === 0);
  if (hasNoChildren) return null;

  return (
    <Popover.Portal>
      <Popover.Content
        className={clsx(autocompleteOptionsVariants({ size }), className)}
        sideOffset={4}
        align="start"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        {children}
      </Popover.Content>
    </Popover.Portal>
  );
}

// Option component
interface AutocompleteOptionProps {
  value: unknown;
  children: ReactNode;
  className?: string;
  disabled?: boolean;
}

export function AutocompleteOption({
  value,
  children,
  className,
  disabled = false,
}: AutocompleteOptionProps) {
  const {
    setSelectedValue,
    options,
    highlightedIndex,
    getOptionValue,
    setIsOpen,
  } = useAutocompleteContext();

  const optionRef = useRef<HTMLDivElement>(null);

  const currentIndex = options.findIndex((option) => {
    if (!getOptionValue) {
      return option === value;
    }
    try {
      return getOptionValue(option) === getOptionValue(value);
    } catch {
      return option === value;
    }
  });

  const isHighlighted = currentIndex === highlightedIndex && currentIndex >= 0;

  // Scroll highlighted option into view
  useEffect(() => {
    if (isHighlighted && optionRef.current) {
      optionRef.current.scrollIntoView({
        block: 'nearest',
        behavior: 'smooth',
      });
    }
  }, [isHighlighted]);

  const handleClick = () => {
    if (!disabled) {
      setSelectedValue(value);
      setIsOpen(false);
    }
  };

  return (
    <div
      ref={optionRef}
      className={clsx(
        'px-3 py-2 cursor-pointer hover:bg-gray-100 transition-colors',
        {
          'bg-blue-50 text-blue-900': isHighlighted,
          'opacity-50 cursor-not-allowed': disabled,
        },
        className
      )}
      onClick={handleClick}
      data-highlighted={isHighlighted}
      data-disabled={disabled}
    >
      {children}
    </div>
  );
}

// Compound component exports
Autocomplete.Input = AutocompleteInput;
Autocomplete.Options = AutocompleteOptions;
Autocomplete.Option = AutocompleteOption;
