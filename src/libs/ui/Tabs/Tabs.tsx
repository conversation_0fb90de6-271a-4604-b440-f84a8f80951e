import { Box, Flex, UnstyledButton } from '@mantine/core';
import styles from './Tabs.module.css';
interface TabsProps {
  active: number;
  tabs: {
    label: string;
    onClick: (index: number) => void;
  }[];
}
export const Tabs = ({ tabs, active }: TabsProps) => {
  return (
    <Flex bg="#f1f2f4" style={{ borderRadius: '0.5rem' }}>
      {tabs.map(({ label, onClick }, index) => (
        <Box flex="1" key={label}>
          <UnstyledButton
            w="100%"
            h="40px"
            ta="center"
            onClick={() => onClick(index)}
            className={active === index ? styles.activeTab : ''}
            style={{ borderRadius: '0.5rem' }}
          >
            {label}
          </UnstyledButton>
        </Box>
      ))}
    </Flex>
  );
};
