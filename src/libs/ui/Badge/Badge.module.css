.container {
  min-height: 1.75rem;
  display: flex;
  align-items: center;
  padding: 0 1.25rem;
  border-radius: 8rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: capitalize;
  white-space: nowrap;
}

.gradientWrapper {
  padding: 1px;
  background: linear-gradient(270deg, #3958d4, #edf421, #9cae8f, #5e79e3);
  background-size: 600% 600%;
  border-radius: 8rem;
  animation: gradientAnimation 10s ease infinite;
  align-self: flex-start;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
