import type { ReactNode } from 'react';
import styles from './Badge.module.css';
import clsx from 'clsx';

export interface BadgeProps {
  color: string;
  background?: string;
  borderColor?: string;
  children: ReactNode;
  variant?: 'default' | 'gradient';
}
export const Badge = ({
  background,
  color,
  children,
  borderColor,
  variant = 'default',
}: BadgeProps) => {
  return (
    <div
      className={clsx(styles.wrapper, {
        [styles.gradientWrapper]: variant === 'gradient',
      })}
    >
      <div
        className={clsx(styles.container, {
          [styles.gradient]: variant === 'gradient',
        })}
        style={{
          backgroundColor: background,
          color,
          borderColor,
        }}
      >
        {children}
      </div>
    </div>
  );
};
