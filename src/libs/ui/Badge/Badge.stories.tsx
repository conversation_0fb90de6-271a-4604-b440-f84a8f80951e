import type { Meta, StoryObj } from '@storybook/react';
import { Badge, type BadgeProps } from './Badge';
import { Text } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';

type Story = StoryObj<typeof Badge>;

const configs: Omit<BadgeProps, 'children'>[] = [
  {
    background: '#89BF77',
    color: '#FFF',
  },
  {
    background: '#B6F5F9',
    color: '#344054',
  },
  {
    background: '#FFF',
    color: '#344054',
    variant: 'gradient',
  },
];

const Wrap = () => {
  return (
    <Flex gap="md" p="lg">
      {configs.map((config, index) => (
        <Badge key={+index} {...config}>
          Text
        </Badge>
      ))}
      <Badge {...configs[2]}>
        <Text lh="2rem">Custom text</Text>
      </Badge>
    </Flex>
  );
};

const meta: Meta<typeof Badge> = {
  title: 'UI/Badge',
  component: Wrap,
};
export default meta;

export const Default: Story = {
  args: {},
};
