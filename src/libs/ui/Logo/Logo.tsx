import LogoEmblemUrl from './assets/logo-emblem.svg';
import LogoFullUrl from './assets/logo-full.svg';
import LogoNameUrl from './assets/logo-name.svg';

type LogoTypes = 'full' | 'name' | 'emblem';

interface LogoProps {
  type?: LogoTypes;
}
export const Logo = ({ type = 'full' }: LogoProps) => {
  const logos: Record<LogoTypes, string> = {
    full: LogoFullUrl,
    emblem: LogoEmblemUrl,
    name: LogoNameUrl,
  };

  return <img src={logos[type]} alt="Logo" width="100%" />;
};
