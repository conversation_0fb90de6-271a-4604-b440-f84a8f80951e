import { Flex, Pagination as MPagination, Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';

import { DefaultItemOption } from '@/types/utility';
import { ITEMS_PER_PAGE_OPTIONS } from '@/constants';

import { getEndIndex, getStartIndex, getTotalPage } from './utils';
import styles from './Pagination.module.css';

interface PaginationProps {
  total: number;
  limitOptions?: DefaultItemOption[];
  itemsPerPage?: string;
  page: number;
  onPageChange: (page: number) => void;
  onChangeItemsPerPage: (page: string) => void;
}

const CLASSES = {
  control: styles.control,
};

export const Pagination = (props: PaginationProps) => {
  const {
    limitOptions = ITEMS_PER_PAGE_OPTIONS,
    itemsPerPage = (props.limitOptions ?? ITEMS_PER_PAGE_OPTIONS)[0].value,
    total,
    page,
    onPageChange,
    onChangeItemsPerPage,
  } = props;
  const { t } = useTranslation();
  const [query] = useSearchParams();

  const startIndex = getStartIndex(page, itemsPerPage);
  const endIndex = getEndIndex(startIndex, itemsPerPage, total);
  const totalPage = getTotalPage(total, itemsPerPage);

  if (!total) {
    return null;
  }

  const handleChangeItemsPerPage = (perPage: string | null) => {
    if (!perPage) {
      return;
    }

    onChangeItemsPerPage(perPage);
  };

  return (
    <div className={styles.container}>
      <div className={styles.limits}>
        <Text size="sm" span>
          {t('common.paginationItemsPerPage')}
        </Text>

        <Flex gap="8">
          {limitOptions.map(({ label, value }) => (
            <button
              key={value}
              className={`${styles.limitButtons} ${itemsPerPage === label ? styles.limitButtonsActive : ''}`}
              onClick={() => handleChangeItemsPerPage(value)}
            >
              {label}
            </button>
          ))}
        </Flex>
      </div>

      {totalPage > 1 && (
        <MPagination
          value={+(query.get('page') || page)}
          total={totalPage}
          onChange={onPageChange}
          classNames={CLASSES}
          size="sm"
          color="dark.8"
        />
      )}

      <Text size="sm">
        {t('common.paginationTotal', { startIndex, endIndex, total })}
      </Text>
    </div>
  );
};
