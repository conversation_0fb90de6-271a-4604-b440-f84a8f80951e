import { cn } from '@/utils';
import React, { forwardRef } from 'react';

// Mantine spacing values mapping to Tailwind classes
const SPACING_MAP = {
  xs: '0.5rem',
  sm: '0.75rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  xxl: '3rem',
} as const;

// Convert Mantine spacing values to CSS custom properties or Tailwind classes
const convertSpacing = (
  value: string | number | undefined,
): string | undefined => {
  if (!value) return undefined;

  if (typeof value === 'number') {
    return `${value}px`;
  }

  if (typeof value === 'string') {
    // Handle Mantine spacing tokens
    if (value in SPACING_MAP) {
      return SPACING_MAP[value as keyof typeof SPACING_MAP];
    }

    // Handle direct CSS values (rem, px, etc.)
    if (value.includes('rem') || value.includes('px') || value.includes('%')) {
      return value;
    }

    // Handle numeric strings
    if (!isNaN(Number(value))) {
      return `${value}px`;
    }
  }

  return value as string;
};

// Convert gap values to Tailwind classes
const convertGap = (gap: string | number | undefined): string | null => {
  if (!gap) return null;

  // Temporarily disable Tailwind gap classes and use inline styles
  // This ensures gap always works regardless of Tailwind CSS issues
  return null;

  // Original logic (commented out for now):
  // const gapStr = String(gap);
  // if (gapStr in GAP_MAP) {
  //   return GAP_MAP[gapStr as keyof typeof GAP_MAP];
  // }
  // return null;
};

export interface FlexProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'color'> {
  // Layout props
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  align?:
    | 'stretch'
    | 'flex-start'
    | 'flex-end'
    | 'center'
    | 'baseline'
    | 'start'
    | 'end';
  justify?:
    | 'flex-start'
    | 'flex-end'
    | 'center'
    | 'space-between'
    | 'space-around'
    | 'space-evenly'
    | 'start'
    | 'end'
    | 'between'
    | 'around'
    | 'evenly'
    | 'left'
    | 'right';
  wrap?: 'nowrap' | 'wrap' | 'wrap-reverse';
  gap?: string | number;

  // Sizing props
  w?: string | number;
  h?: string | number;
  miw?: string | number;
  maw?: string | number;
  mih?: string | number;
  mah?: string | number;

  // Spacing props
  p?: string | number;
  px?: string | number;
  py?: string | number;
  pl?: string | number;
  pr?: string | number;
  pt?: string | number;
  pb?: string | number;
  m?: string | number;
  mx?: string | number;
  my?: string | number;
  mt?: string | number;
  mb?: string | number;
  ml?: string | number;
  mr?: string | number;

  // Position props
  pos?: 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky';
  top?: string | number;
  right?: string | number;
  bottom?: string | number;
  left?: string | number;

  // Style props
  bg?: string;
  c?: string;
  ta?: 'left' | 'center' | 'right' | 'justify';
  lh?: string | number;

  // Semantic props
  component?: keyof JSX.IntrinsicElements;

  // Flex-specific props
  flex?: string | number;
}

export const Flex = forwardRef<HTMLDivElement, FlexProps>(
  (
    {
      children,
      className,
      direction = 'row',
      align,
      justify,
      wrap,
      gap,
      w,
      h,
      miw,
      maw,
      mih,
      mah,
      p,
      px,
      py,
      pl,
      pr,
      pt,
      pb,
      m,
      mx,
      my,
      mt,
      mb,
      ml,
      mr,
      pos,
      top,
      right,
      bottom,
      left,
      bg,
      c,
      ta,
      lh,
      component = 'div',
      flex,
      style,
      ...props
    },
    ref,
  ) => {
    // Build Tailwind classes
    const classes = cn(
      'flex',

      // Direction
      direction === 'column' && 'flex-col',
      direction === 'row-reverse' && 'flex-row-reverse',
      direction === 'column-reverse' && 'flex-col-reverse',

      // Align items
      align === 'center' && 'items-center',
      align === 'flex-start' && 'items-start',
      align === 'start' && 'items-start',
      align === 'flex-end' && 'items-end',
      align === 'end' && 'items-end',
      align === 'stretch' && 'items-stretch',
      align === 'baseline' && 'items-baseline',

      // Justify content
      justify === 'center' && 'justify-center',
      justify === 'flex-start' && 'justify-start',
      justify === 'start' && 'justify-start',
      justify === 'flex-end' && 'justify-end',
      justify === 'end' && 'justify-end',
      justify === 'space-between' && 'justify-between',
      justify === 'between' && 'justify-between',
      justify === 'space-around' && 'justify-around',
      justify === 'around' && 'justify-around',
      justify === 'space-evenly' && 'justify-evenly',
      justify === 'evenly' && 'justify-evenly',
      justify === 'left' && 'justify-start',
      justify === 'right' && 'justify-end',

      // Wrap
      wrap === 'wrap' && 'flex-wrap',
      wrap === 'nowrap' && 'flex-nowrap',
      wrap === 'wrap-reverse' && 'flex-wrap-reverse',

      // Gap
      convertGap(gap) || '',

      // Position
      pos === 'relative' && 'relative',
      pos === 'absolute' && 'absolute',
      pos === 'fixed' && 'fixed',
      pos === 'sticky' && 'sticky',

      // Text alignment
      ta === 'left' && 'text-left',
      ta === 'center' && 'text-center',
      ta === 'right' && 'text-right',
      ta === 'justify' && 'text-justify',

      className,
    );

    // Build inline styles for props that don't have Tailwind equivalents
    const inlineStyles: React.CSSProperties = {
      ...style,

      // Sizing
      ...(w && { width: convertSpacing(w) }),
      ...(h && { height: convertSpacing(h) }),
      ...(miw && { minWidth: convertSpacing(miw) }),
      ...(maw && { maxWidth: convertSpacing(maw) }),
      ...(mih && { minHeight: convertSpacing(mih) }),
      ...(mah && { maxHeight: convertSpacing(mah) }),

      // Spacing
      ...(p && { padding: convertSpacing(p) }),
      ...(px && {
        paddingLeft: convertSpacing(px),
        paddingRight: convertSpacing(px),
      }),
      ...(py && {
        paddingTop: convertSpacing(py),
        paddingBottom: convertSpacing(py),
      }),
      ...(pl && { paddingLeft: convertSpacing(pl) }),
      ...(pr && { paddingRight: convertSpacing(pr) }),
      ...(pt && { paddingTop: convertSpacing(pt) }),
      ...(pb && { paddingBottom: convertSpacing(pb) }),
      ...(m && { margin: convertSpacing(m) }),
      ...(mx && {
        marginLeft: convertSpacing(mx),
        marginRight: convertSpacing(mx),
      }),
      ...(my && {
        marginTop: convertSpacing(my),
        marginBottom: convertSpacing(my),
      }),
      ...(mt && { marginTop: convertSpacing(mt) }),
      ...(mb && { marginBottom: convertSpacing(mb) }),
      ...(ml && { marginLeft: convertSpacing(ml) }),
      ...(mr && { marginRight: convertSpacing(mr) }),

      // Position
      ...(top && { top: convertSpacing(top) }),
      ...(right && { right: convertSpacing(right) }),
      ...(bottom && { bottom: convertSpacing(bottom) }),
      ...(left && { left: convertSpacing(left) }),

      // Colors and background
      ...(bg && { backgroundColor: bg }),
      ...(c && { color: c }),

      // Text
      ...(lh && { lineHeight: lh }),

      // Flex
      ...(flex && { flex: flex }),

      // Handle custom gap values that don't map to Tailwind
      ...(convertGap(gap) === null && gap && { gap: convertSpacing(gap) }),
    };

    const Component = component as React.ElementType;

    return (
      <Component ref={ref} className={classes} style={inlineStyles} {...props}>
        {children}
      </Component>
    );
  },
);

Flex.displayName = 'Flex';
