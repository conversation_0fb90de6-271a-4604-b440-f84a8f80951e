import { ReactNode } from 'react';
import clsx from 'clsx';
import { Box } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import styles from './Alert.module.css';

interface AlertProps {
  type?: 'info' | 'error';
  icon?: ReactNode;
  children: ReactNode;
}
export const Alert = ({ icon, children, type }: AlertProps) => (
  <Flex
    align="center"
    px=".75rem"
    py="0.5rem"
    w="100%"
    className={clsx(styles.container, {
      [styles.error]: type === 'error',
    })}
  >
    {icon ? (
      <Box w="28" mr="10">
        {icon}
      </Box>
    ) : null}
    <Box>{children}</Box>
  </Flex>
);
