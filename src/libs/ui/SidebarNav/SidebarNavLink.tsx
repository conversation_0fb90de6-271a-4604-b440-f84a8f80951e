import { Flex, Text } from '@mantine/core';
import { ReactNode } from 'react';
import { NavLink } from 'react-router-dom';
import styles from './SidebarNavLink.module.css';
import clsx from 'clsx';

export interface SidebarNavLinkProps {
  label: string;
  path: string;
  icon: ReactNode;
}
export const SidebarNavLink = ({ label, path, icon }: SidebarNavLinkProps) => (
  <NavLink
    className={({ isActive }) =>
      clsx(styles.container, { [styles.active]: isActive })
    }
    to={path}
  >
    <Flex align="center" p="0.75rem">
      {icon}
      <Text fw="500" ml="0.5rem">
        {label}
      </Text>
    </Flex>
  </NavLink>
);
