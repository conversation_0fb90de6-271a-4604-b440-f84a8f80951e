import { Flex, Text } from '@mantine/core';
import { SidebarNavLink, type SidebarNavLinkProps } from './SidebarNavLink';

export interface SidebarNavProps {
  title?: string;
  links: SidebarNavLinkProps[];
}
export const SidebarNav = ({ title, links }: SidebarNavProps) => {
  return (
    <Flex direction="column">
      {title ? (
        <Text c="#73757C" p="0.75rem" mt="0.25rem">
          {title}
        </Text>
      ) : (
        ''
      )}
      {links.map(({ icon, label, path }) => (
        <SidebarNavLink key={label} icon={icon} label={label} path={path} />
      ))}
    </Flex>
  );
};
