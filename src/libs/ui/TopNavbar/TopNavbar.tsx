import type { ReactNode } from 'react';
import { Flex, Text } from '@mantine/core';

import styles from './TopNavbar.module.css';

interface TopNavbarPtops {
  title?: string;
  children?: ReactNode;
}
export const TopNavbar = ({ title, children }: TopNavbarPtops) => {
  return (
    <Flex
      className={styles.container}
      align="center"
      justify="space-between"
      mih="75px"
    >
      {title ? (
        <Text size="1.25rem" fw="700" mr="0.75rem">
          {title}
        </Text>
      ) : null}
      {children}
    </Flex>
  );
};
