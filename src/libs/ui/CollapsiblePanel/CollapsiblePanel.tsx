import { AccordionChevron, Box, Collapse, Group } from '@mantine/core';

import styles from './CollapsiblePanel.module.css';
import { ReactNode } from 'react';
import { useDisclosure } from '@mantine/hooks';
import clsx from 'clsx';

interface AccordionProps {
  header: ReactNode;
  content: ReactNode;
  startOpen?: boolean;
  variant?: 'default' | 'clean';
}

export const CollapsiblePanel = ({
  header,
  content,
  startOpen = false,
  variant = 'default',
}: AccordionProps) => {
  const [opened, { toggle }] = useDisclosure(startOpen);

  return (
    <Box w="100%" className={styles.container}>
      <Group>
        <Box
          className={clsx(styles.header, {
            [styles.cleanHeader]: variant === 'clean',
          })}
        >
          {header}
          {content ? (
            <button
              className={styles.collapseButton}
              onClick={toggle}
              data-open={opened}
            >
              <AccordionChevron />
            </button>
          ) : null}
        </Box>
      </Group>

      <Collapse in={opened}>{content}</Collapse>
    </Box>
  );
};
