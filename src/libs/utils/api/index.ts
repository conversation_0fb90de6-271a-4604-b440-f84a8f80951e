import { getActiveClinic } from '@/libs/clinics/utils/activeClinic';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';

const BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost';

type RequestOptions = {
  headers?: Record<string, string>;
  suppressAuthRedirect?: boolean;
};

interface BaseRequestProps {
  url: string;
  withApi?: boolean;
}

interface PostProps<T> extends BaseRequestProps {
  body?: T;
  method?: 'POST' | 'PUT' | 'PATCH';
  isFormData?: boolean;
}

type GetProps = BaseRequestProps & {
  options?: RequestOptions;
};

function getCookie(name: string): string | null {
  const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
  return match ? match[2] : null;
}

async function refreshXsrfToken(): Promise<void> {
  await fetch(`${BASE_URL}/sanctum/csrf-cookie`, {
    credentials: 'include',
  });
}

function getXsrfTokenFromCookie(): string | null {
  const token = getCookie('XSRF-TOKEN');
  return token ? decodeURIComponent(token) : null;
}

async function request<T>(
  url: string,
  {
    method,
    body,
    options = {},
  }: {
    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
    body?: T;
    options?: RequestOptions;
  },
) {
  await refreshXsrfToken();

  const xsrfToken = getXsrfTokenFromCookie();
  const headers = {
    'Content-Type': 'application/json',
    'X-XSRF-TOKEN': xsrfToken || '',
    'Highfive-Clinic': getActiveClinic()?.id,
    Accept: 'application/json',
    ...options.headers,
  };

  const response = await fetch(`${BASE_URL}${url}`, {
    method,
    headers,
    body,
    credentials: 'include',
  } as never);

  if (response.status === 401 && !options.suppressAuthRedirect) {
    window.location.replace(ROUTERS_PATH.login);
    throw new Error('Unauthorized');
  }

  return response;
}

export async function get<T>({
  url,
  withApi = true,
  options,
}: GetProps): Promise<T> {
  const response = await request<T>(`${withApi ? '/api' : ''}${url}`, {
    method: 'GET',
    options,
  });

  if (response.status === 204) {
    return response as T;
  }

  const data = await response.json();

  if (!response.ok) {
    throw { apiResponse: response, data };
  }

  return data;
}

export async function post<T, K = unknown>({
  url,
  body,
  method = 'POST',
  withApi = true,
}: PostProps<K>): Promise<T> {
  const response = await request<K>(`${withApi ? '/api' : ''}${url}`, {
    method,
    body: body ? JSON.stringify(body) : undefined,
    credentials: 'include',
  } as never);

  if (response.ok) {
    try {
      return await response.json();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      return response as T;
    }
  } else {
    throw { apiResponse: response, data: await response.json() };
  }
}

export async function deleteApi<T>({
  url,
  body,
  withApi = true,
}: BaseRequestProps & { body?: unknown }): Promise<T> {
  const response = await request(`${withApi ? '/api' : ''}${url}`, {
    method: 'DELETE',
    body: body ? JSON.stringify(body) : undefined,
  } as never);

  return await response.json();
}
