import { Flex, Menu, Text, UnstyledButton } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import type { ClinicType } from '@/types/common';
import { ROUTERS_PATH } from '@/apps/shop/routes/routes';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import SwapArrowIcon from './assets/swap-arrow.svg?react';
import BackIcon from './assets/back.svg?react';

interface SwapClinicProps {
  list: ClinicType[];
  current: ClinicType;
}
export const SwapClinic = ({ current, list }: SwapClinicProps) => {
  const { setActiveClinic } = useAccountStore();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleClinicSelect = (clinic: ClinicType) => {
    if (current.id !== clinic.id) {
      setActiveClinic({
        id: clinic.id,
        name: clinic.name,
        hasAnyVendorConnected: clinic.hasAnyVendorConnected,
        // TODO: Status hardcoded
        status: 'DONE',
      });
      window.location.reload();
    }
  };

  return (
    <Menu>
      <Flex>
        <Text size="1.25rem" fw="700" mr="0.75rem">
          {current.name}
        </Text>
        <Menu.Target>
          <UnstyledButton>
            <SwapArrowIcon />
          </UnstyledButton>
        </Menu.Target>
      </Flex>
      <Menu.Dropdown>
        {list
          .filter(({ id }) => id !== current.id)
          .map((clinic) => (
            <Menu.Item
              key={clinic.id}
              onClick={() => handleClinicSelect(clinic)}
            >
              {clinic.name}
            </Menu.Item>
          ))}
        <Menu.Item
          leftSection={<BackIcon />}
          onClick={() => navigate(ROUTERS_PATH.clinicManagement)}
        >
          {t('sidebar.clinicManagement')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};
