import { Divider, Flex, LoadingOverlay, Text } from '@mantine/core';
import { useTranslation } from 'react-i18next';

import { Button } from '@/components';
import { getPriceString } from '@/utils';

import { getSubtotalItemsText } from '@/libs/cart/utils/getSubtotalItemsText';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import styles from './CartSummary.module.css';
import { ReactNode } from 'react';
import { CartItemType, CartVendorType } from '../../types';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';

interface CartSummaryProps {
  action: {
    onAction: () => void;
    actionButtonText: string;
    isActionBlocked: boolean;
  };
  extraInfo?: ReactNode;
  isLoading?: boolean;
  headerAction?: {
    label: string;
    handleClick: VoidFunction;
  };
}

const getSavings = (items: CartVendorType['items']) => {
  const { gpoSavings, vendorSavings } = items.reduce<{
    gpoSavings: number;
    vendorSavings: number;
  }>(
    (acc, item: CartItemType) => {
      const offer = item.product.offers.find(
        ({ id }) => id === item.productOfferId,
      )!;
      const { gpoSavings, vendorSavings } = getProductOfferComputedData(offer);

      acc.gpoSavings += gpoSavings * item.quantity;
      acc.vendorSavings += vendorSavings * item.quantity;

      return acc;
    },
    {
      gpoSavings: 0,
      vendorSavings: 0,
    },
  );

  return { gpoSavings, vendorSavings };
};

export const CartSummary = ({
  action,
  isLoading = false,
  headerAction,
  extraInfo = null,
}: CartSummaryProps) => {
  const { onAction, actionButtonText, isActionBlocked } = action;
  const { t } = useTranslation();
  const { vendors, total, itemsCount } = useCartStore();

  return (
    <div className={styles.section}>
      <Flex align="center" justify="space-between" mb="1.5rem">
        <Text fw="bold">Your Cart</Text>

        {headerAction ? (
          <div className={styles.clearCart}>
            <Button
              p="0"
              size="xs"
              color="transparent"
              variant="transparent"
              onClick={headerAction.handleClick}
            >
              <Text c="#666" size="sm">
                {headerAction.label}
              </Text>
            </Button>
          </div>
        ) : null}
      </Flex>

      {vendors.map(({ id, subtotal, items, name, shippingFee, total }) => {
        const { vendorSavings } = getSavings(items);

        return (
          <div key={id} className={styles.vendorSummary}>
            <Text mb="0.5rem" size="1.125rem" fw="700">
              {name}
            </Text>

            <div className={styles.row}>
              <Text size="sm" c="dark.6">
                {getSubtotalItemsText(items.length)}
              </Text>
              <Text size="sm">{getPriceString(subtotal)}</Text>
            </div>

            {/* {+gpoSavings ? (
              <div className={styles.row}>
                <Text size="sm">GPO Savings</Text>
                <Text size="sm" td="line-through">
                  {getPriceString(gpoSavings)}
                </Text>
              </div>
            ) : null} */}

            {+vendorSavings ? (
              <div className={styles.row}>
                <Text size="sm">Vendor Savings</Text>
                <Text size="sm" td="line-through">
                  {getPriceString(vendorSavings)}
                </Text>
              </div>
            ) : null}

            <div className={styles.row}>
              {+shippingFee === 0 ? (
                <>
                  <Text size="sm" c="#4A8B34">
                    {t('common.shipping')}
                  </Text>
                  <Text size="sm" c="#4A8B34">
                    {t('common.free')}
                  </Text>
                </>
              ) : (
                <>
                  <Text size="sm">{t('common.shipping')}</Text>
                  <Text size="sm">{getPriceString(+shippingFee)}</Text>
                </>
              )}
            </div>

            <div className={styles.row}>
              <Text size="sm" c="dark.6" fw="700">
                {t('common.subtotal')}
              </Text>
              <Text size="sm" fw="700">
                {getPriceString(total)}
              </Text>
            </div>
            <Divider my="1rem" />
          </div>
        );
      })}

      {extraInfo ? (
        <>
          {extraInfo}
          <Divider my="1rem" />
        </>
      ) : null}

      <Text size="1rem" fw="400">
        {getSubtotalItemsText(itemsCount)}
      </Text>

      <Text size="2rem" m="0.5rem" lh="2.3rem" fw="700">
        {getPriceString(total)}
      </Text>

      <Button
        size="sm"
        h="2rem"
        m="0.5rem"
        onClick={onAction}
        disabled={isActionBlocked || isLoading}
        loading={isLoading}
      >
        {actionButtonText}
      </Button>
      <LoadingOverlay visible={isLoading} />
    </div>
  );
};
