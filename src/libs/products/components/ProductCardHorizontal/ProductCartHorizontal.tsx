import { Link } from 'react-router-dom';
import { Box, Flex, Image } from '@mantine/core';
import { FavoriteButton } from '@/libs/products/components/FavoriteButton/FavoriteButton';
import { GpoRecommendedTag } from '@/libs/gpo/components/GpoRecommendedTag/GpoRecommendedTag';
import styles from './ProductCardHorizontal.module.css';
import { SpecialInstructionIconList } from '../SpecialInstructionIconList/SpecialInstructionIconList';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import type { ReactNode } from 'react';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { ProductType } from '@/types';

interface ProductCartHorizontalProps {
  productOfferId: string;
  product: Partial<ProductType> & {
    id: ProductType['id'];
    name: ProductType['name'];
    imageUrl: ProductType['imageUrl'];
  };
  content: ReactNode;
  actions: ReactNode;
}

export const ProductCartHorizontal = ({
  productOfferId,
  product,
  content,
  actions,
}: ProductCartHorizontalProps) => {
  const productUrl = getProductUrl(product.id, productOfferId);
  const {
    id,
    name,
    imageUrl,
    isFavorite,
    isControlledSubstance = false,
    isHazardous = false,
    requiresColdShipping = false,
    requiresPedigree = false,
    requiresPrescription = false,
    offers = [],
  } = product;

  const offer = offers.find(({ id }) => id === productOfferId);

  const specialInstructions = {
    isControlledSubstance,
    isHazardous,
    requiresColdShipping,
    requiresPedigree,
    requiresPrescription,
  };

  return (
    <Flex className={styles.container}>
      <div className={styles.imageContainer}>
        {offer?.isRecommended !== undefined && (
          <GpoRecommendedTag
            isRecommended={offer.isRecommended}
            size="sm"
            top="0.75rem"
          />
        )}
        {isFavorite !== undefined && (
          <Box pos="absolute" right="0.5rem" top="0.5rem">
            <FavoriteButton productId={id} isFavorite={isFavorite} />
          </Box>
        )}
        <Flex pos="absolute" right="0.5rem" bottom="0.5rem">
          <SpecialInstructionIconList {...specialInstructions} />
        </Flex>
        <Link to={productUrl}>
          <Image src={imageUrl} fallbackSrc={defaultProductImgUrl} alt={name} />
        </Link>
      </div>
      <Flex justify="space-between" w="100%">
        <Box w="85%">{content}</Box>
        <Box w="15%">{actions}</Box>
      </Flex>
    </Flex>
  );
};
