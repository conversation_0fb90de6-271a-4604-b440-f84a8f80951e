import { FormEventHandler, useCallback, useState } from 'react';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useCartProductMapState } from '@/libs/cart/hooks/useCartProductMapState';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { AddToCartButton } from './components/AddToCartButton/AddToCartButton';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '../AddToCartInput/AddToCartInput';

interface AddToCartFormProps {
  productOfferId: string;
  increments: number;
}
export const AddToCartForm = ({
  productOfferId,
  increments,
}: AddToCartFormProps) => {
  const { addToCart, updatingProductIds } = useCartStore();
  const [amount, setAmount] = useState(increments ?? 1);
  const cartProductMapState = useCartProductMapState();
  const isUpdating = updatingProductIds.has(productOfferId);

  const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = ({
    amount: newAmount,
  }) => {
    setAmount(newAmount);
  };

  const offerQuantityOnCart =
    cartProductMapState[productOfferId]?.quantity ?? 0;

  const handleAddToCartClick: FormEventHandler<HTMLFormElement> = useCallback(
    (event) => {
      event.preventDefault();

      addToCart({
        productOfferId,
        quantity: offerQuantityOnCart + amount,
        // TODO: Handle error better
        onError: () => {},
      });
    },
    [productOfferId, addToCart, offerQuantityOnCart, amount],
  );

  return (
    <form onSubmit={handleAddToCartClick}>
      <Flex gap="12px" flex="grow">
        <AddToCartInput
          originalAmount={increments}
          minIncrement={increments}
          onUpdate={handleQuantityUpdate}
        />
        <AddToCartButton
          quantityInCart={offerQuantityOnCart}
          isLoading={isUpdating}
          fullWidth
        />
      </Flex>
    </form>
  );
};
