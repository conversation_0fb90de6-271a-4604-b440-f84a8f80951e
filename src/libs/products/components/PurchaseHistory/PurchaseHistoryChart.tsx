import { getPriceString } from '@/utils';
import { Box, Divider, Flex, Text } from '@mantine/core';
import { useCallback, useEffect, useState } from 'react';
import { Bar<PERSON>hart, Bar, XAxis, ResponsiveContainer } from 'recharts';
import {
  fetchPurchaseHistoryData,
  renderCustomAxisTick,
  renderCustomBarLabel,
} from './utils';
import { PurchaseHistoryChartData } from './types';

const SummaryRow = ({
  label,
  value,
  lastItem = false,
}: {
  label: string;
  value: string | number;
  lastItem?: boolean;
}) => (
  <>
    <Flex w="100%" miw="100%" align="center" gap="0.25rem">
      <Text miw="90px" c="rgba(51, 51, 51, 0.50)" size="0.75rem">
        {label}
      </Text>
      <Text miw="125px" size="1rem" fw="500">
        {value}
      </Text>
    </Flex>
    {!lastItem && <Divider my="0.75rem" />}
  </>
);

export interface PurchaseHistoryChartProps {
  productId: string;
}

export const PurchaseHistoryChart = ({
  productId,
}: PurchaseHistoryChartProps) => {
  const [chartData, setChartData] = useState<PurchaseHistoryChartData | null>(
    null,
  );

  const fetchData = useCallback(async () => {
    const data = await fetchPurchaseHistoryData(productId);
    setChartData(data);
  }, [productId, setChartData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return chartData ? (
    <Flex>
      <Flex direction="column" w="200px">
        <SummaryRow
          label="Total Spend"
          value={getPriceString(chartData.summary.totalSpent)}
        />

        <SummaryRow
          label="Total Orders"
          value={chartData.summary.totalOrders}
        />

        <SummaryRow
          label="Avg Quantity"
          value={chartData.summary.averageQuantityPerOrder}
        />

        <SummaryRow
          label="Lowest Price"
          value={getPriceString(chartData.summary.lowestUnitPrice)}
        />

        <SummaryRow
          label="Highest Price"
          value={getPriceString(chartData.summary.highestUnitPrice)}
          lastItem
        />
      </Flex>
      <Divider orientation="vertical" mx="md" />
      <Box pt="2rem" w="558px">
        <ResponsiveContainer width="100%" height={180}>
          <BarChart
            data={chartData.data}
            margin={{
              top: 20,
              right: 5,
              left: 5,
            }}
          >
            <XAxis
              dataKey="label"
              tick={renderCustomAxisTick}
              interval={0}
              alignmentBaseline="middle"
              axisLine={{ stroke: 'rgba(0, 0, 0, 0.10)' }}
              tickLine={false}
            />

            <Bar
              dataKey="totalQuantity"
              fill="#BDDCF0"
              label={renderCustomBarLabel}
              barSize={20}
            />
          </BarChart>
        </ResponsiveContainer>
      </Box>
    </Flex>
  ) : null;
};
