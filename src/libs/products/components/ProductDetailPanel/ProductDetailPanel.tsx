import { Accordion, Box, Title } from '@mantine/core';
import { ReactElement } from 'react';
import styles from './ProductDetailPanel.module.css';

type ProductDetailPanelProps = {
  title: string;
  children?: ReactElement | null;
};
export const ProductDetailPanel = ({
  title,
  children,
}: ProductDetailPanelProps) => {
  if (!children) {
    return null;
  }

  return (
    <Box className={styles.container}>
      <Accordion defaultValue="1">
        <Accordion.Item value="1">
          <Accordion.Control className={styles.titleWrap}>
            <Title order={4}>{title}</Title>
          </Accordion.Control>

          <Accordion.Panel bg="#fff">{children}</Accordion.Panel>
        </Accordion.Item>
      </Accordion>
    </Box>
  );
};
