import { Text, UnstyledButton } from '@mantine/core';
import { Flex } from '@/libs/ui/Flex/Flex';
import ClipboardIcon from './assets/clipboard.svg?react';

interface DownloadChecklistProps {
  url: string;
}
export const DownloadChecklist = ({ url }: DownloadChecklistProps) => (
  <UnstyledButton component="a" href={url} mb="md" download>
    <Flex align="center" gap="0.3rem">
      <ClipboardIcon />
      <Text c="#4942D4" size="xs" fw="500">
        Download Checklist
      </Text>
    </Flex>
  </UnstyledButton>
);
