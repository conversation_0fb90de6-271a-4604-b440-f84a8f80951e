name: Deploy Staging

on:
  push:
    branches:
      - main

jobs:
  deploy:
    environment: staging

    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
          tools: composer:v2
          coverage: none

      - name: Require Forge CLI
        run: composer global require laravel/forge-cli

      - name: Deploy Site
        run: |
          forge server:switch highfive-backend-staging
          forge deploy staging.app.highfive.vet
        env:
          FORGE_API_TOKEN: ${{ secrets.FORGE_API_TOKEN }}
