import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';
import path from 'path';

const APPS = ['shop', 'gpo-portal'];

export default defineConfig(({ mode }) => {
  const app = process.env.APP || APPS[0];

  console.log(`Selected app: ${app}`);

  return {
    server: {
      port: 3000 + APPS.indexOf(app),
      open: true,
    },
    plugins: [react(), svgr(), tsconfigPaths()],
    test: {
      environment: 'jsdom',
      globals: true,
      setupFiles: path.resolve(__dirname, './vitest.setup.js'),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    root: path.resolve(__dirname, `src/apps/${app}`),
    base: './',
    build: {
      outDir: path.resolve(__dirname, `build/${app}`),
      sourcemap: mode === 'development',
      emptyOutDir: true,
    },
  };
});
